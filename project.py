# to run: python -m waitress --listen=0.0.0.0:8080 project:app
# access here:         http://10.95.21.30:8080/
# mini PC access here: http://10.95.24.184:8080/

from flask import Flask, request, jsonify, render_template, session, redirect, url_for, flash, send_file
from ldap3 import Server, Connection, ALL
from functools import wraps
import sqlite3
import os
import logging
import pandas as pd
from openpyxl import load_workbook, Workbook
from openpyxl.worksheet.table import Table, TableStyleInfo
from openpyxl.utils import get_column_letter
import tempfile
from datetime import datetime
import io
from zoneinfo import ZoneInfo
from Refresh_OJT_detail import refresh


# Initialize the Flask application
app = Flask(__name__)
app.secret_key = "os.environ.get('SECRET_KEY', os.urandom(32))"

# Set up logging
logging.basicConfig(level=logging.DEBUG)

# Database setup
DB_PATH = os.path.join('Data', 'DataNew.db')

# === TIMEZONE CONVERSION FUNCTION ===
def convert_utc_to_eastern_formatted(utc_timestamp_str):
    """
    Convert UTC timestamp to Eastern Standard Time and format as YYYY-MM-DD HH:MM
    """
    if not utc_timestamp_str:
        return None
    try:
        # Parse the timestamp (handle both with and without timezone info)
        if utc_timestamp_str.endswith('Z'):
            utc_dt = datetime.fromisoformat(utc_timestamp_str.replace('Z', '+00:00'))
        elif '+' in utc_timestamp_str or utc_timestamp_str.endswith('-04:00'):
            utc_dt = datetime.fromisoformat(utc_timestamp_str)
        else:
            # Assume UTC if no timezone info
            utc_dt = datetime.fromisoformat(utc_timestamp_str + '+00:00')

        # Convert to Eastern timezone
        eastern_tz = ZoneInfo('America/New_York')
        eastern_dt = utc_dt.astimezone(eastern_tz)

        # Format as YYYY-MM-DD HH:MM
        return eastern_dt.strftime('%Y-%m-%d %H:%M')
    except Exception as e:
        print(f"⚠️ Error converting timestamp {utc_timestamp_str}: {e}")
        return utc_timestamp_str
os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

# LDAP Configuration
LDAP_SERVER = '10.95.5.42'
LDAP_DOMAIN = 'LGENSOL'
LDAP_BASE_DN = 'DC=LGENSOL,DC=com'

# Add this near the top of the file, where other paths are defined
FLEX_CHART_PATH = os.path.join('data', 'Facility Flex Chart LOCAL.xlsx')

# Make sure the data directory exists
os.makedirs(os.path.dirname(FLEX_CHART_PATH), exist_ok=True)

def get_db_connection():
    """Create a database connection and return the connection"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # This enables column access by name
    return conn

def ldap_authenticate(username, password):
    try:
        # Initialize server
        server = Server(LDAP_SERVER, get_info=ALL)
        user_dn = f'{username}@{LDAP_DOMAIN}'

        # Attempt to bind with credentials
        conn = Connection(server, user=user_dn, password=password)
        if conn.bind():
            return True
        return False
    except Exception as e:
        logging.error(f"LDAP Error: {str(e)}")
        flash('LDAP server is unavailable')
        return False

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            return redirect(url_for('login'))

        # Get current user's access level from database (not session to ensure it's current)
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT access_level FROM Authorization WHERE employee_login = ?", (session['username'],))
        user_auth = cursor.fetchone()
        conn.close()

        if not user_auth or user_auth[0] != 'Admin':
            flash('Access denied. Admin privileges required.')
            return redirect(url_for('index'))

        return f(*args, **kwargs)
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        # First check if user exists in Authorization table
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM Authorization WHERE employee_login = ?", (username,))
        authorized_user = cursor.fetchone()
        conn.close()

        if not authorized_user:
            flash('You are not authorized or not in facilities to access this system.')
            return redirect(url_for('login'))

        # If user is authorized, proceed with LDAP authentication
        if ldap_authenticate(username, password):
            session['username'] = username
            # Store the user's access level in the session
            session['access_level'] = authorized_user[3]  # access_level is the 4th column
            session['department'] = authorized_user[1]    # department is the 2nd column
            return redirect(url_for('index'))
        else:
            flash('Invalid credentials')
            return redirect(url_for('login'))

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.pop('username', None)
    return redirect(url_for('login'))

# Protect routes that require authentication
@app.route('/')
@login_required
def index():
    return render_template('dashboard.html')

@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html')

@app.route('/training')
@login_required
def training():
    refresh()
    return render_template('training.html')

@app.route('/api/check-workorders', methods=['GET'])
def check_workorders():
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Modified query to get crew from OrgChart table instead of employeelist
        cursor.execute("""
            SELECT 
                w.jpnum AS activity_number,
                w.laborname AS assigned_to,
                CASE
                    WHEN o.shift LIKE 'A-Crew%' THEN 'A'
                    WHEN o.shift LIKE 'B-Crew%' THEN 'B'
                    WHEN o.shift LIKE 'C-Crew%' THEN 'C'
                    WHEN o.shift LIKE 'D-Crew%' THEN 'D'
                    ELSE o.shift
                END AS crew,
                w.description,
                o.title AS owning_department,
                CASE SUBSTR(w.assetnum, 1, 15)
                    WHEN 'U1UFAC00501-001' THEN 'SAFETY-STAGE 1'
                    WHEN 'U1UFAC00501-002' THEN 'SAFETY-STAGE 2'
                    WHEN 'U1UFAC00502-001' THEN 'BOILER-STAGE 1'
                    WHEN 'U1UFAC00502-002' THEN 'BOILER-STAGE 2'
                    WHEN 'U1UFAC00502-003' THEN 'BOILER-STAGE 3'
                    WHEN 'U1UFAC00502-004' THEN 'BOILER-STAGE 4'
                    WHEN 'U1UFAC00506-001' THEN 'OPERATOR-STAGE 1'
                    WHEN 'U1UFAC00506-002' THEN 'OPERATOR-STAGE 2'
                    WHEN 'U1UFAC00506-003' THEN 'OPERATOR-STAGE 3'
                    WHEN 'U1UFAC00506-004' THEN 'OPERATOR-STAGE 4'
                    WHEN 'U1UFAC00503-001' THEN 'ELECTRICAL-STAGE 1'
                    WHEN 'U1UFAC00503-002' THEN 'ELECTRICAL-STAGE 2'
                    WHEN 'U1UFAC00503-004' THEN 'ELECTRICAL-STAGE 3'
                    WHEN 'U1UFAC00503-005' THEN 'ELECTRICAL-STAGE 4'
                    WHEN 'U1UFAC00507-001' THEN 'OPERATOR-STAGE 1'
                    WHEN 'U1UFAC00507-002' THEN 'OPERATOR-STAGE 2'
                    WHEN 'U1UFAC00507-003' THEN 'OPERATOR-STAGE 3'
                    WHEN 'U1UFAC00507-004' THEN 'OPERATOR-STAGE 4'
                    WHEN 'U1UFAC00504-001' THEN 'HVAC-STAGE 1'
                    WHEN 'U1UFAC00504-002' THEN 'HVAC-STAGE 2'
                    WHEN 'U1UFAC00504-003' THEN 'HVAC-STAGE 3'
                    WHEN 'U1UFAC00504-004' THEN 'HVAC-STAGE 4'
                    WHEN 'U1UFAC00505-001' THEN 'MECHANICAL-STAGE 1'
                    WHEN 'U1UFAC00505-002' THEN 'MECHANICAL-STAGE 2'
                    WHEN 'U1UFAC00505-003' THEN 'MECHANICAL-STAGE 3'
                    WHEN 'U1UFAC00505-004' THEN 'MECHANICAL-STAGE 4'
                    ELSE 'UNKNOWN STAGE'
                END AS stage,
                w.status,
                w.changedate AS status_date,
                w.parent AS work_order_number
            FROM OJT_WO_status w
            LEFT JOIN OrgChart o ON w.laborname = o.name
            ORDER BY crew, assigned_to, stage, status_date
        """)


        
        records = [dict(row) for row in cursor.fetchall()]
        
        # Log the number of records found for debugging
        print(f"Found {len(records)} work orders")
        
        conn.close()

        return jsonify({
            'status': 'success',
            'records': records
        })

    except Exception as e:
        print("Error in check_workorders:", str(e))
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

@app.route('/analytics')
def analytics():
    refresh()
    return render_template('ganttchart.html')

@app.route('/todo-workorders')
@login_required
def todo_workorders():
    return render_template('todo_workorders.html')

@app.route('/authorization')
@admin_required
def authorization():
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    cursor.execute('''CREATE TABLE IF NOT EXISTS Authorization (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        department TEXT NOT NULL,
        employee_login TEXT NOT NULL,
        access_level TEXT NOT NULL,
        UNIQUE(department, employee_login)
    )''')
    conn.commit()

    cursor.execute("SELECT * FROM Authorization ORDER BY access_level, employee_login")
    authorizations = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('authorization.html', authorizations=authorizations)

@app.route('/api/authorizations', methods=['POST'])
@admin_required
def add_authorization():
    data = request.get_json()

    if not data or 'department' not in data or 'employee_login' not in data or 'access_level' not in data:
        return jsonify({'error': 'Missing required fields'}), 400

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(
            "INSERT INTO Authorization (department, employee_login, access_level) VALUES (?, ?, ?)",
            (data['department'], data['employee_login'], data['access_level'])
        )
        conn.commit()

        # Return the new authorization data including the ID
        new_id = cursor.lastrowid
        return jsonify({
            'id': new_id,
            'department': data['department'],
            'employee_login': data['employee_login'],
            'access_level': data['access_level']
        }), 201
    except sqlite3.IntegrityError:
        return jsonify({'error': 'Authorization already exists'}), 409
    finally:
        conn.close()

@app.route('/api/authorizations/<int:auth_id>', methods=['DELETE'])
@admin_required
def delete_authorization(auth_id):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # First, check if this is an Admin user being deleted
    cursor.execute("SELECT access_level, employee_login FROM Authorization WHERE id = ?", (auth_id,))
    user_to_delete = cursor.fetchone()

    if not user_to_delete:
        conn.close()
        return jsonify({'error': 'Authorization not found'}), 404

    access_level, _ = user_to_delete

    # If trying to delete an Admin, check if it's the last one
    if access_level == 'Admin':
        cursor.execute("SELECT COUNT(*) FROM Authorization WHERE access_level = 'Admin'")
        admin_count = cursor.fetchone()[0]

        if admin_count <= 1:
            conn.close()
            return jsonify({'error': 'Cannot delete the last Admin user. Please add another Admin user before deleting this one.'}), 400

    # Proceed with deletion
    cursor.execute("DELETE FROM Authorization WHERE id = ?", (auth_id,))

    if cursor.rowcount == 0:
        conn.close()
        return jsonify({'error': 'Authorization not found'}), 404

    conn.commit()
    conn.close()

    return jsonify({'message': 'Authorization deleted successfully'})

@app.route('/calendar')
@login_required
def calendar():
    return render_template('calendar.html')

def get_crew_color(crew):
    """Generate colors based on crew assignment"""
    crew_colors = {
        'A': '#6b007b',    # Red
        'B': '#118dff',    # Teal
        'C': '#e66c37',    # Blue
        'D': '#12239e',    # Green
    }

    # Handle empty or null crew values
    if not crew or crew == '':
        return '#CCCCCC'  # Gray for unassigned

    # If crew not in predefined colors, generate a hash-based color
    if crew not in crew_colors:
        hash_value = hash(crew) % 360
        return f'hsl({hash_value}, 70%, 70%)'

    return crew_colors[crew]

@app.route('/api/events', methods=['GET'])
def get_events():
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Query TODO_WorkOrder table with shift datetime fields and crew
        cursor.execute("""
            SELECT
                wonum,
                "Equipment Description" AS equipment,
                description,
                "Shift Start DateTime" AS shift_start,
                datetime("Shift Start DateTime", '+1 hour') AS shift_end,
                ownergroup,
                jpnum,
                Crew
            FROM TODO_WorkOrder
            WHERE "Shift Start DateTime" IS NOT NULL
        """)
        # cursor.execute("""
        #     SELECT
        #         wonum,
        #         "Equipment Description" AS equipment,
        #         description,
        #         schedstart AS shift_start,
        #         schedfinish AS shift_end,
        #         ownergroup,
        #         jpnum,
        #         Crew
        #     FROM TODO_WorkOrder
        #     WHERE "Shift Start DateTime" IS NOT NULL
        # """)

        events = []
        for row in cursor.fetchall():
            row_dict = dict(row)

            # Convert shift datetime strings to Eastern Time
            try:
                # Parse the shift datetime strings (these are already timezone-naive from processing)
                shift_start_str = row_dict['shift_start']
                shift_end_str = row_dict['shift_end']

                # Handle different datetime formats
                if shift_start_str and shift_end_str:
                    if 'T' in shift_start_str:
                        # ISO format
                        shift_start = datetime.fromisoformat(shift_start_str.replace('Z', ''))
                        shift_end = datetime.fromisoformat(shift_end_str.replace('Z', ''))
                    else:
                        # Standard datetime format
                        shift_start = datetime.strptime(shift_start_str, '%Y-%m-%d %H:%M:%S')
                        shift_end = datetime.strptime(shift_end_str, '%Y-%m-%d %H:%M:%S')

                    # Assume these are already in Eastern Time (from workorder_in_draft.py processing)
                    eastern_tz = ZoneInfo("America/New_York")
                    eastern_start = shift_start.replace(tzinfo=eastern_tz)
                    eastern_end = shift_end.replace(tzinfo=eastern_tz)
                else:
                    # Skip events without valid shift times
                    continue

                # Create the event with converted times and crew-based coloring
                crew = row_dict.get('Crew', '')
                event = {
                    'id': row_dict['wonum'],
                    'title': row_dict['description'],
                    'equipment': row_dict['equipment'],
                    'start': eastern_start.isoformat(),
                    'end': eastern_end.isoformat(),
                    'ownergroup': row_dict['ownergroup'],
                    'jpnum': row_dict['jpnum'],
                    'crew': crew,
                    'color': get_crew_color(crew)
                }
                events.append(event)

            except Exception as date_error:
                print(f"Error converting datetime for event {row_dict['wonum']}: {date_error}")
                continue

        conn.close()
        return jsonify(events)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/todo-workorders', methods=['GET'])
@login_required
def get_todo_workorders():
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Query TODO_WorkOrder table for TO-DO items using shift datetime fields and crew
        cursor.execute("""
            SELECT
                wonum,
                description,
                "Shift Start DateTime" as shift_start,
                "Shift End DateTime" as shift_end,
                ownergroup,
                jpnum,
                Crew
            FROM TODO_WorkOrder
            WHERE "Shift Start DateTime" IS NOT NULL
            AND "Shift End DateTime" IS NOT NULL
            ORDER BY "Shift Start DateTime" ASC
        """)

        workorders = []
        for row in cursor.fetchall():
            row_dict = dict(row)

            # Convert shift datetime strings to Eastern Time
            try:
                # Parse the shift datetime strings (these are already timezone-naive from processing)
                shift_start_str = row_dict['shift_start']
                shift_end_str = row_dict['shift_end']

                # Handle different datetime formats
                if shift_start_str and shift_end_str:
                    if 'T' in shift_start_str:
                        # ISO format
                        shift_start = datetime.fromisoformat(shift_start_str.replace('Z', ''))
                        shift_end = datetime.fromisoformat(shift_end_str.replace('Z', ''))
                    else:
                        # Standard datetime format
                        shift_start = datetime.strptime(shift_start_str, '%Y-%m-%d %H:%M:%S')
                        shift_end = datetime.strptime(shift_end_str, '%Y-%m-%d %H:%M:%S')

                    # Assume these are already in Eastern Time (from workorder_in_draft.py processing)
                    eastern_tz = ZoneInfo("America/New_York")
                    eastern_start = shift_start.replace(tzinfo=eastern_tz)
                    eastern_end = shift_end.replace(tzinfo=eastern_tz)
                else:
                    # Skip work orders without valid shift times
                    continue

                # Determine status based on dates
                now = datetime.now(eastern_tz)
                if eastern_end < now:
                    status = 'overdue'
                elif eastern_start <= now <= eastern_end:
                    status = 'inprogress'
                elif eastern_start > now:
                    status = 'pending'
                else:
                    status = 'completed'

                # Get crew information and color
                crew = row_dict.get('Crew', '')

                # Create the work order object with crew-based coloring
                workorder = {
                    'id': row_dict['wonum'],
                    'text': row_dict['description'] or 'No Description',
                    'start_date': eastern_start.strftime('%Y-%m-%d %H:%M'),
                    'end_date': eastern_end.strftime('%Y-%m-%d %H:%M'),
                    'duration': (eastern_end - eastern_start).days + 1,
                    'ownergroup': row_dict['ownergroup'],
                    'jpnum': row_dict['jpnum'],
                    'crew': crew,
                    'color': get_crew_color(crew),
                    'status': status,
                    'progress': 0.5 if status == 'inprogress' else (1.0 if status == 'completed' else 0.0)
                }
                workorders.append(workorder)

            except Exception as date_error:
                print(f"Error converting datetime for work order {row_dict['wonum']}: {date_error}")
                continue

        conn.close()
        return jsonify({'data': workorders})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/events', methods=['POST'])
@login_required
def create_event():
    data = request.json
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute("""
            INSERT INTO Events (title, start_time, end_time, event_type)
            VALUES (?, ?, ?, ?)
        """, (data['title'], data['start'], data['end'], data['type']))
        conn.commit()
        event_id = cursor.lastrowid
        conn.close()
        return jsonify({'success': True, 'id': event_id})
    except Exception as e:
        conn.close()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/events/<int:event_id>', methods=['DELETE', 'PUT'])
@login_required
def manage_event(event_id):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        if request.method == 'DELETE':
            cursor.execute("DELETE FROM Events WHERE id = ?", (event_id,))
        else:  # PUT
            data = request.json
            cursor.execute("""
                UPDATE Events
                SET title = ?, start_time = ?, end_time = ?, event_type = ?
                WHERE id = ?
            """, (data['title'], data['start'], data['end'], data['type'], event_id))

        conn.commit()
        conn.close()
        return jsonify({'success': True})
    except Exception as e:
        conn.close()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/download-flex-chart', methods=['GET'])
@login_required
def download_flex_chart():
    try:
        # Define the path to the Flex Chart file
        flex_chart_path = os.path.join('data', 'Flex Chart.xlsx')

        # Check if the file exists
        if not os.path.exists(flex_chart_path):
            return jsonify({
                'status': 'error',
                'error': f'Flex Chart file not found: {flex_chart_path}'
            }), 404

        # Connect to database and fetch the data
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Execute the query to get all work order data
        cursor.execute("""
            SELECT
                w.*,
                o.name as orgchart_name,
                o.title as orgchart_title,
                o.position as orgchart_position,
                o.department as orgchart_department,
                o.shift as orgchart_shift,
                o.team as orgchart_team
            FROM OJT_WO_status w
            LEFT JOIN OrgChart o ON w.laborname = o.name
            ORDER BY o.shift, w.laborname, w.assetnum, w.changedate
        """)

        # Convert to pandas DataFrame
        data = [dict(row) for row in cursor.fetchall()]

        # Convert timezone fields to Eastern Standard Time and format
        for record in data:
            if record.get('changedate'):
                record['changedate'] = convert_utc_to_eastern_formatted(record['changedate'])
            if record.get('statusdate'):
                record['statusdate'] = convert_utc_to_eastern_formatted(record['statusdate'])

        print("Converted timestamps to Eastern Standard Time")

        df = pd.DataFrame(data)

        print(f"Found {len(data)} work orders for export")
        if df.empty:
            print("Warning: No data found to export")

        # Load the existing Flex Chart workbook
        print("Loading existing Flex Chart workbook...")
        workbook = load_workbook(flex_chart_path)

        # Remove existing AppData sheet if it exists
        if 'AppData' in workbook.sheetnames:
            print("Removing existing AppData sheet...")
            del workbook['AppData']

        # Create new AppData sheet
        print("Creating new AppData sheet...")
        app_data_sheet = workbook.create_sheet('AppData')

        # Remove existing Crew Pivot sheet if it exists
        if 'Crew Pivot' in workbook.sheetnames:
            print("Removing existing Crew Pivot sheet...")
            del workbook['Crew Pivot']

        # Create new Crew Pivot sheet
        print("Creating new Crew Pivot sheet...")
        crew_pivot_sheet = workbook.create_sheet('Technician Crew Pivot')

        # Write headers and data
        if not df.empty:
            print(f"Writing {len(df)} rows of data to Excel...")
            headers = list(df.columns)

            # Write headers
            print("Writing headers...")
            for col_idx, header in enumerate(headers, 1):
                app_data_sheet.cell(row=1, column=col_idx, value=header)

            # Write data in batches for better performance
            print("Writing data rows...")
            batch_size = 1000
            for batch_start in range(0, len(df), batch_size):
                batch_end = min(batch_start + batch_size, len(df))
                print(f"Writing batch {batch_start//batch_size + 1}: rows {batch_start+1} to {batch_end}")

                for row_idx in range(batch_start, batch_end):
                    excel_row = row_idx + 2  # +2 because Excel is 1-indexed and we have headers
                    row_data = df.iloc[row_idx]
                    for col_idx, value in enumerate(row_data, 1):
                        app_data_sheet.cell(row=excel_row, column=col_idx, value=value)

            # Create a new table with the data
            print("Creating Excel table...")
            # Define the table range
            end_column = len(headers)
            end_row = len(df) + 1  # +1 for header row

            # Convert column number to letter
            end_column_letter = get_column_letter(end_column)

            table_range = f"A1:{end_column_letter}{end_row}"
            print(f"Table range: {table_range}")

            # Create the table
            table = Table(displayName="AppDataTable", ref=table_range)

            # Add table style
            style = TableStyleInfo(
                name="TableStyleMedium2",
                showFirstColumn=False,
                showLastColumn=False,
                showRowStripes=True,
                showColumnStripes=False
            )
            table.tableStyleInfo = style

            # Add the table to the worksheet
            app_data_sheet.add_table(table)
            print("Excel table created successfully")

        # Now create the Crew Pivot sheet
        print("Fetching OrgChart data for Crew Pivot...")

        # Reconnect to database for OrgChart data
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get shift and name data from OrgChart
        cursor.execute("""
            SELECT shift, name
            FROM OrgChart
            WHERE shift IS NOT NULL AND shift != '' AND name IS NOT NULL AND name != '' AND team = "Technician"
            ORDER BY shift, name
        """)

        org_data = [dict(row) for row in cursor.fetchall()]

        if org_data:
            print(f"Found {len(org_data)} OrgChart records for Crew Pivot")

            # Group data by shift
            shift_groups = {}
            for record in org_data:
                shift = record['shift']
                name = record['name']
                if shift not in shift_groups:
                    shift_groups[shift] = []
                shift_groups[shift].append(name)

            # Write headers (shifts) in the first row
            col_idx = 1
            for shift in sorted(shift_groups.keys()):
                crew_pivot_sheet.cell(row=1, column=col_idx, value=shift)
                col_idx += 1

            # Write names under each shift column
            col_idx = 1
            for shift in sorted(shift_groups.keys()):
                names = shift_groups[shift]
                for row_idx, name in enumerate(names, 2):  # Start from row 2
                    crew_pivot_sheet.cell(row=row_idx, column=col_idx, value=name)
                col_idx += 1

            print("Technician Crew Pivot sheet populated successfully")
        else:
            print("No OrgChart data found for Technician Crew Pivot")

        # Create HVAC Crew Pivot sheet
        if 'HVAC Crew Pivot' in workbook.sheetnames:
            print("Removing existing HVAC Crew Pivot sheet...")
            del workbook['HVAC Crew Pivot']

        print("Creating HVAC Crew Pivot sheet...")
        hvac_crew_pivot_sheet = workbook.create_sheet('HVAC Crew Pivot')

        # Get HVAC team data from OrgChart
        cursor.execute("""
            SELECT shift, name
            FROM OrgChart
            WHERE shift IS NOT NULL AND shift != '' AND name IS NOT NULL AND name != '' AND team = "HVAC"
            ORDER BY shift, name
        """)

        hvac_data = [dict(row) for row in cursor.fetchall()]

        if hvac_data:
            print(f"Found {len(hvac_data)} HVAC team records")

            # Group HVAC data by shift
            hvac_shift_groups = {}
            for record in hvac_data:
                shift = record['shift']
                name = record['name']
                if shift not in hvac_shift_groups:
                    hvac_shift_groups[shift] = []
                hvac_shift_groups[shift].append(name)

            # Write headers (shifts) in the first row
            col_idx = 1
            for shift in sorted(hvac_shift_groups.keys()):
                hvac_crew_pivot_sheet.cell(row=1, column=col_idx, value=shift)
                col_idx += 1

            # Write names under each shift column
            col_idx = 1
            for shift in sorted(hvac_shift_groups.keys()):
                names = hvac_shift_groups[shift]
                for row_idx, name in enumerate(names, 2):  # Start from row 2
                    hvac_crew_pivot_sheet.cell(row=row_idx, column=col_idx, value=name)
                col_idx += 1

            print("HVAC Crew Pivot sheet populated successfully")
        else:
            print("No HVAC team data found")

        # Create Operation Crew Pivot sheet
        if 'Operation Crew Pivot' in workbook.sheetnames:
            print("Removing existing Operation Crew Pivot sheet...")
            del workbook['Operation Crew Pivot']

        print("Creating Operation Crew Pivot sheet...")
        operation_crew_pivot_sheet = workbook.create_sheet('Operation Crew Pivot')

        # Get Operation team data from OrgChart
        cursor.execute("""
            SELECT shift, name
            FROM OrgChart
            WHERE shift IS NOT NULL AND shift != '' AND name IS NOT NULL AND name != '' AND team = "Operation"
            ORDER BY shift, name
        """)

        operation_data = [dict(row) for row in cursor.fetchall()]

        if operation_data:
            print(f"Found {len(operation_data)} Operation team records")

            # Group Operation data by shift
            operation_shift_groups = {}
            for record in operation_data:
                shift = record['shift']
                name = record['name']
                if shift not in operation_shift_groups:
                    operation_shift_groups[shift] = []
                operation_shift_groups[shift].append(name)

            # Write headers (shifts) in the first row
            col_idx = 1
            for shift in sorted(operation_shift_groups.keys()):
                operation_crew_pivot_sheet.cell(row=1, column=col_idx, value=shift)
                col_idx += 1

            # Write names under each shift column
            col_idx = 1
            for shift in sorted(operation_shift_groups.keys()):
                names = operation_shift_groups[shift]
                for row_idx, name in enumerate(names, 2):  # Start from row 2
                    operation_crew_pivot_sheet.cell(row=row_idx, column=col_idx, value=name)
                col_idx += 1

            print("Operation Crew Pivot sheet populated successfully")
        else:
            print("No Operation team data found")

        # Create Other Crew Pivot sheet
        if 'Other Crew Pivot' in workbook.sheetnames:
            print("Removing existing Other Crew Pivot sheet...")
            del workbook['Other Crew Pivot']

        print("Creating Other Crew Pivot sheet...")
        other_crew_pivot_sheet = workbook.create_sheet('Other Crew Pivot')

        # Get Other team data from OrgChart
        cursor.execute("""
            SELECT shift, name
            FROM OrgChart
            WHERE shift IS NOT NULL AND shift != '' AND name IS NOT NULL AND name != '' AND team = "Other"
            ORDER BY shift, name
        """)

        other_data = [dict(row) for row in cursor.fetchall()]

        if other_data:
            print(f"Found {len(other_data)} Other team records")

            # Group Other data by shift
            other_shift_groups = {}
            for record in other_data:
                shift = record['shift']
                name = record['name']
                if shift not in other_shift_groups:
                    other_shift_groups[shift] = []
                other_shift_groups[shift].append(name)

            # Write headers (shifts) in the first row
            col_idx = 1
            for shift in sorted(other_shift_groups.keys()):
                other_crew_pivot_sheet.cell(row=1, column=col_idx, value=shift)
                col_idx += 1

            # Write names under each shift column
            col_idx = 1
            for shift in sorted(other_shift_groups.keys()):
                names = other_shift_groups[shift]
                for row_idx, name in enumerate(names, 2):  # Start from row 2
                    other_crew_pivot_sheet.cell(row=row_idx, column=col_idx, value=name)
                col_idx += 1

            print("Other Crew Pivot sheet populated successfully")
        else:
            print("No Other team data found")

        # Create LatestOJT sheet
        if 'LatestOJT' in workbook.sheetnames:
            print("Removing existing LatestOJT sheet...")
            del workbook['LatestOJT']

        print("Creating LatestOJT sheet...")
        latest_ojt_sheet = workbook.create_sheet('LatestOJT')

        # Get latest OJT data (excluding CLOSE status)
        cursor.execute("""
            SELECT
                w.*,
                o.name as orgchart_name,
                o.title as orgchart_title,
                o.position as orgchart_position,
                o.department as orgchart_department,
                o.shift as orgchart_shift,
                o.team as orgchart_team
            FROM OJT_WO_status w
            LEFT JOIN OrgChart o ON w.laborname = o.name
            WHERE w.status != 'CLOSE'
            ORDER BY w.parent, w.statusdate DESC
        """)

        latest_ojt_data = [dict(row) for row in cursor.fetchall()]

        # Convert timezone fields to Eastern Standard Time and format for LatestOJT
        for record in latest_ojt_data:
            if record.get('changedate'):
                record['changedate'] = convert_utc_to_eastern_formatted(record['changedate'])
            if record.get('statusdate'):
                record['statusdate'] = convert_utc_to_eastern_formatted(record['statusdate'])

        if latest_ojt_data:
            print(f"Found {len(latest_ojt_data)} OJT records (excluding CLOSE)")
            print("Converted LatestOJT timestamps to Eastern Standard Time")

            # Group by parent and keep only the latest statusdate for each parent
            from collections import defaultdict
            parent_groups = defaultdict(list)

            for record in latest_ojt_data:
                parent = record['parent']
                parent_groups[parent].append(record)

            # Keep only the latest record for each parent
            latest_records = []
            for parent, records in parent_groups.items():
                # Sort by statusdate (latest first) and take the first one
                latest_record = max(records, key=lambda x: x['changedate'] if x['changedate'] else "")
                latest_records.append(latest_record)

            print(f"Filtered to {len(latest_records)} latest records per parent")

            # Convert to DataFrame for easier handling
            latest_df = pd.DataFrame(latest_records)

            # Write headers
            if not latest_df.empty:
                print("Writing LatestOJT data...")
                headers = list(latest_df.columns)

                # Write headers
                for col_idx, header in enumerate(headers, 1):
                    latest_ojt_sheet.cell(row=1, column=col_idx, value=header)

                # Write data in batches
                batch_size = 1000
                for batch_start in range(0, len(latest_df), batch_size):
                    batch_end = min(batch_start + batch_size, len(latest_df))
                    print(f"Writing LatestOJT batch {batch_start//batch_size + 1}: rows {batch_start+1} to {batch_end}")

                    for row_idx in range(batch_start, batch_end):
                        excel_row = row_idx + 2  # +2 because Excel is 1-indexed and we have headers
                        row_data = latest_df.iloc[row_idx]
                        for col_idx, value in enumerate(row_data, 1):
                            latest_ojt_sheet.cell(row=excel_row, column=col_idx, value=value)

                # Create a table for the LatestOJT data
                print("Creating LatestOJT Excel table...")
                end_column = len(headers)
                end_row = len(latest_df) + 1  # +1 for header row
                end_column_letter = get_column_letter(end_column)
                table_range = f"A1:{end_column_letter}{end_row}"

                latest_table = Table(displayName="LatestOJTTable", ref=table_range)
                style = TableStyleInfo(
                    name="TableStyleMedium2",
                    showFirstColumn=False,
                    showLastColumn=False,
                    showRowStripes=True,
                    showColumnStripes=False
                )
                latest_table.tableStyleInfo = style
                latest_ojt_sheet.add_table(latest_table)

                print("LatestOJT sheet populated successfully")
            else:
                print("No LatestOJT data to write")
        else:
            print("No LatestOJT data found")

        # Create OrgChart sheet
        if 'OrgChart' in workbook.sheetnames:
            print("Removing existing OrgChart sheet...")
            del workbook['OrgChart']

        print("Creating OrgChart sheet...")
        orgchart_sheet = workbook.create_sheet('OrgChart')

        # Get all data from OrgChart table
        cursor.execute("""
            SELECT * FROM OrgChart
            ORDER BY name
        """)

        orgchart_data = [dict(row) for row in cursor.fetchall()]

        if orgchart_data:
            print(f"Found {len(orgchart_data)} OrgChart records")

            # Convert to DataFrame for easier handling
            orgchart_df = pd.DataFrame(orgchart_data)

            # Write headers
            headers = list(orgchart_df.columns)
            for col_idx, header in enumerate(headers, 1):
                orgchart_sheet.cell(row=1, column=col_idx, value=header)

            # Write data in batches
            batch_size = 1000
            for batch_start in range(0, len(orgchart_df), batch_size):
                batch_end = min(batch_start + batch_size, len(orgchart_df))
                print(f"Writing OrgChart batch {batch_start//batch_size + 1}: rows {batch_start+1} to {batch_end}")

                for row_idx in range(batch_start, batch_end):
                    excel_row = row_idx + 2  # +2 because Excel is 1-indexed and we have headers
                    row_data = orgchart_df.iloc[row_idx]
                    for col_idx, value in enumerate(row_data, 1):
                        orgchart_sheet.cell(row=excel_row, column=col_idx, value=value)

            # Create a table for the OrgChart data
            print("Creating OrgChart Excel table...")
            end_column = len(headers)
            end_row = len(orgchart_df) + 1  # +1 for header row
            end_column_letter = get_column_letter(end_column)
            table_range = f"A1:{end_column_letter}{end_row}"

            orgchart_table = Table(displayName="OrgChartTable", ref=table_range)
            style = TableStyleInfo(
                name="TableStyleMedium2",
                showFirstColumn=False,
                showLastColumn=False,
                showRowStripes=True,
                showColumnStripes=False
            )
            orgchart_table.tableStyleInfo = style
            orgchart_sheet.add_table(orgchart_table)

            print("OrgChart sheet populated successfully")
        else:
            print("No OrgChart data found")

        # Close database connection
        conn.close()

        # Hide all the newly created sheets
        print("Hiding all newly created sheets...")
        if 'AppData' in workbook.sheetnames:
            workbook['AppData'].sheet_state = 'hidden'
            print("Hidden AppData sheet")

        if 'Technician Crew Pivot' in workbook.sheetnames:
            workbook['Technician Crew Pivot'].sheet_state = 'hidden'
            print("Hidden Technician Crew Pivot sheet")

        if 'HVAC Crew Pivot' in workbook.sheetnames:
            workbook['HVAC Crew Pivot'].sheet_state = 'hidden'
            print("Hidden HVAC Crew Pivot sheet")

        if 'Operation Crew Pivot' in workbook.sheetnames:
            workbook['Operation Crew Pivot'].sheet_state = 'hidden'
            print("Hidden Operation Crew Pivot sheet")

        if 'Other Crew Pivot' in workbook.sheetnames:
            workbook['Other Crew Pivot'].sheet_state = 'hidden'
            print("Hidden Other Crew Pivot sheet")

        if 'LatestOJT' in workbook.sheetnames:
            workbook['LatestOJT'].sheet_state = 'hidden'
            print("Hidden LatestOJT sheet")

        if 'OrgChart' in workbook.sheetnames:
            workbook['OrgChart'].sheet_state = 'hidden'
            print("Hidden OrgChart sheet")

        print("All sheets hidden successfully")

        # Save to memory buffer
        output_buffer = io.BytesIO()
        workbook.save(output_buffer)
        output_buffer.seek(0)

        print(f"Excel file created successfully, size: {output_buffer.getbuffer().nbytes} bytes")

        # Generate filename with current date
        current_date = datetime.now().strftime('%Y-%m-%d')
        filename = f'Flex Chart {current_date}.xlsx'

        print(f"Sending file: {filename}")

        # Return the modified file
        return send_file(
            output_buffer,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        print(f"Error during download: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/org-chart', methods=['GET'])
def get_org_chart():
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM OrgChart')
        rows = cursor.fetchall()
        return jsonify([dict(row) for row in rows])
    finally:
        conn.close()

@app.route('/api/org-chart', methods=['POST'])
def update_org_chart():
    try:
        data = request.json
        conn = get_db_connection()
        cursor = conn.cursor()

        # Clear existing data
        cursor.execute('DELETE FROM OrgChart')

        # Try to insert with all columns
        for node in data:
            cursor.execute('''
                INSERT INTO OrgChart (id, parentId, name, title, position, department, shift, phone, image, team)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                node['id'],
                node['parentId'],
                node['name'],
                node.get('title', ''),
                node.get('position', ''),
                node.get('department', ''),  # Make sure department is included
                node.get('shift', ''),
                node.get('phone', ''),
                node.get('image', ''),
                node.get('team', '')
            ))

        conn.commit()
        return jsonify({'success': True})
    except Exception as e:
        print(f"Error updating org chart: {e}")  # Add logging
        return jsonify({'success': False, 'error': str(e)}), 500
    finally:
        conn.close()

# Initialize the database table
def init_org_chart_table():
    conn = get_db_connection()
    try:
        cursor = conn.cursor()

        # Check if the table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='OrgChart'")
        table_exists = cursor.fetchone() is not None

        if table_exists:
            # Check if we need to drop the email column
            cursor.execute("PRAGMA table_info(OrgChart)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if 'email' in column_names:
                print("Dropping email column from OrgChart table...")
                try:
                    # Create a new table without the email column
                    cursor.execute('''
                        CREATE TABLE OrgChart_new (
                            id TEXT PRIMARY KEY,
                            parentId TEXT,
                            name TEXT NOT NULL,
                            title TEXT,
                            position TEXT,
                            department TEXT,
                            shift TEXT,
                            phone TEXT,
                            image TEXT,
                            team TEXT
                        )
                    ''')

                    # Copy data from the old table to the new one
                    cursor.execute('''
                        INSERT INTO OrgChart_new (id, parentId, name, title, position, department, shift, phone, image, team)
                        SELECT id, parentId, name, title, position, department, shift, phone, image, '' as team FROM OrgChart
                    ''')

                    # Drop the old table
                    cursor.execute('DROP TABLE OrgChart')

                    # Rename the new table to the original name
                    cursor.execute('ALTER TABLE OrgChart_new RENAME TO OrgChart')

                    conn.commit()
                    print("Email column dropped successfully")
                except sqlite3.OperationalError as e:
                    print(f"Error dropping email column: {e}")
                    conn.rollback()

            # Check if the title column exists
            if 'title' not in column_names:
                print("Adding missing columns to OrgChart table...")
                try:
                    cursor.execute("ALTER TABLE OrgChart ADD COLUMN title TEXT")
                    cursor.execute("ALTER TABLE OrgChart ADD COLUMN shift TEXT")
                    conn.commit()
                    print("Columns added successfully")
                except sqlite3.OperationalError as e:
                    print(f"Error adding columns: {e}")

        # Create the table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS OrgChart (
                id TEXT PRIMARY KEY,
                parentId TEXT,
                name TEXT NOT NULL,
                title TEXT,
                position TEXT,
                department TEXT,
                shift TEXT,
                phone TEXT,
                image TEXT,
                team TEXT
            )
        ''')

        # Add team column if it doesn't exist (for existing databases)
        if table_exists:
            cursor.execute("PRAGMA table_info(OrgChart)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if 'team' not in column_names:
                print("Adding team column to OrgChart table...")
                try:
                    cursor.execute('ALTER TABLE OrgChart ADD COLUMN team TEXT')
                    conn.commit()
                    print("Team column added successfully")
                except sqlite3.OperationalError as e:
                    print(f"Error adding team column: {e}")

        # Check if table is empty and insert root node if it is
        cursor.execute('SELECT COUNT(*) FROM OrgChart')
        if cursor.fetchone()[0] == 0:
            cursor.execute('''
                INSERT INTO OrgChart (id, parentId, name, title, position, department, shift, phone, image, team)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                "1",
                "",
                "Ultium Cells Facility",
                "",
                "",
                "",
                "",
                "",
                "static/images/Ultium-New-Logo-icon.png",
                ""
            ))

        conn.commit()
    finally:
        conn.close()

# Call this during application startup
init_org_chart_table()

@app.route('/organization-chart')
@login_required
def organization_chart():
    return render_template('organization_chart.html')

@app.route('/test-sidebar')
def test_sidebar():
    return render_template('dashboard.html')

@app.route('/api/departments', methods=['GET'])
def get_departments():
    try:
        print("Fetching departments...")  # Debug log
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Get unique departments
        cursor.execute("""
            SELECT DISTINCT owning_department 
            FROM WorkOrder 
            WHERE owning_department IS NOT NULL AND owning_department != ''
            ORDER BY owning_department
        """)
        departments = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        
        print(f"Found {len(departments)} departments")  # Debug log
        return jsonify(departments)
    except Exception as e:
        print(f"Error in get_departments: {str(e)}")  # Debug log
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/filtered-workorders', methods=['GET'])
def filtered_workorders():
    try:
        # Get filter parameters
        department = request.args.get('department')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Validate parameters
        if not department:
            return jsonify({
                'status': 'error',
                'message': 'Department parameter is required'
            }), 400
            
        # Connect to database
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Build query with parameters
        query = """
            SELECT work_order_number, assigned_to, description,
                   status, status_date, activity_number, owning_department, stage
            FROM WorkOrder
            WHERE owning_department = ?
        """
        params = [department]
        
        # Add date filters if provided
        if start_date:
            query += " AND status_date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND status_date <= ?"
            params.append(end_date + " 23:59:59")  # Include the entire end day
            
        query += " ORDER BY assigned_to, stage, status_date"
        
        # Execute query
        cursor.execute(query, params)
        records = [dict(row) for row in cursor.fetchall()]
        
        # Log the number of records for debugging
        print(f"Filtered query returned {len(records)} records for department: {department}")
        
        conn.close()

        return jsonify({
            'status': 'success',
            'records': records
        })
    except Exception as e:
        print(f"Error in filtered_workorders: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/user-access')
@login_required
def get_user_access():
    # Get the current user's access level directly from the database
    # instead of relying on the session data which might be stale
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Get the current username from the session
        username = session.get('username', '')
        if not username:
            return jsonify({'access_level': '', 'department': '', 'is_admin': False})

        # Query the Authorization table for the latest access level
        cursor.execute("SELECT department, access_level FROM Authorization WHERE employee_login = ?", (username,))
        user_auth = cursor.fetchone()

        if user_auth:
            # Update the session with the latest data
            session['access_level'] = user_auth[1]  # access_level
            session['department'] = user_auth[0]    # department

            return jsonify({
                'access_level': user_auth[1],
                'department': user_auth[0],
                'is_admin': user_auth[1] == 'Admin'
            })
        else:
            return jsonify({'access_level': '', 'department': '', 'is_admin': False})
    except Exception as e:
        print(f"Error fetching user access level: {e}")
        return jsonify({'access_level': '', 'department': '', 'is_admin': False, 'error': str(e)})
    finally:
        if conn:
            conn.close()

@app.route('/api/export-orgchart', methods=['POST'])
@login_required
def export_orgchart():
    """Export OrgChart data to Excel by running the Export OrgChart.py script"""
    try:
        import subprocess
        import os

        print("Starting OrgChart export...")

        # Path to the Export OrgChart.py script
        script_path = os.path.join(os.getcwd(), 'Export OrgChart.py')

        # Check if the script exists
        if not os.path.exists(script_path):
            print(f"Export script not found at: {script_path}")
            return jsonify({'error': 'Export script not found'}), 404

        # Run the Export OrgChart.py script
        print(f"Running export script: {script_path}")
        result = subprocess.run(['python', script_path],
                              capture_output=True,
                              text=True,
                              cwd=os.getcwd())

        if result.returncode != 0:
            print(f"Export script failed with return code {result.returncode}")
            print(f"Error output: {result.stderr}")
            return jsonify({'error': f'Export script failed: {result.stderr}'}), 500

        print("Export script completed successfully")
        print(f"Script output: {result.stdout}")

        # Look for the generated Excel file
        # The script creates a file named 'OrgChart_export.xlsx' in the current directory
        export_file_path = os.path.join(os.getcwd(), 'OrgChart_export.xlsx')

        if not os.path.exists(export_file_path):
            # Try alternative common names
            alternative_paths = [
                'OrgChart_Export.xlsx',
                'OrgChart.xlsx',
                'export.xlsx'
            ]

            for alt_path in alternative_paths:
                full_alt_path = os.path.join(os.getcwd(), alt_path)
                if os.path.exists(full_alt_path):
                    export_file_path = full_alt_path
                    break
            else:
                print(f"Export file not found at: {export_file_path}")
                return jsonify({'error': 'Export file not generated'}), 500

        print(f"Sending export file: {export_file_path}")

        # Send the file
        return send_file(
            export_file_path,
            as_attachment=True,
            download_name='OrgChart_Export.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        print(f"Error in export_orgchart: {str(e)}")
        return jsonify({'error': str(e)}), 500

if __name__ == "__main__":
    app.run(host="127.0.0.1", port=3001, debug=True)

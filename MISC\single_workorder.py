import requests
import os
import urllib3
import sqlite3
import pandas as pd
import datetime

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def load_last_refresh_date():
    filename = "last_refresh_date_OJT.txt"
    if os.path.exists(filename):
        with open(filename, "r") as f:
            return f.read().strip()

def save_last_refresh_date(iso_str):
    with open("last_refresh_date_OJT.txt", "w") as f:
        f.write(iso_str)

LAST_REFRESH_DATE = load_last_refresh_date()
print(f"LAST REFRESH DATE in UTC: {LAST_REFRESH_DATE}\n")

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "oslcwodetail"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
DB_PATH = os.path.join("data", "Data.db")

# Fixed WHERE clause with correct spacing
where_clause = (
    'spi_wm:siteid="UTIL.GM" and '
    'spi_wm:ownergroup="GM.UT.U" and '
    'spi_wm:zancestor="U1UFAC005" and '
    'spi_wm:istask=0 and '
    f'dcterms:modified>"{LAST_REFRESH_DATE}"'
)

params = {
    "oslc.where": where_clause,
    "oslc.select": "spi_wm:wonum,spi_wm:assignment{spi_wm:laborname},wostatus",
    "lean": "1"
}


headers = {
    "Accept": "application/json",
    "apikey": API_KEY
}

url = f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}"

print(f"🔍 Querying work orders detail with WHERE clause: {where_clause}")
resp = requests.get(url, headers=headers, params=params, verify=False)

# Connect to DB
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# Create table if not exists (do not drop to preserve data)
cursor.execute("""
CREATE TABLE IF NOT EXISTS OJT_WO_status (
    wonum TEXT,
    changeby TEXT,
    changedate TEXT,
    status TEXT,
    laborname TEXT
)
""")

# Extract records
rows = []
for member in resp.json().get("member", []):
    wonum = member.get("wonum") or member.get("spi_wm:wonum")

    # Get latest laborname from assignment block (if exists)
    laborname = None
    assignments = member.get("spi_wm:assignment", [])
    if assignments:
        latest = max(assignments, key=lambda a: a.get("spi_wm:changedate", ""))
        laborname = latest.get("spi_wm:laborname")

    # Iterate over wostatus records
    for ws in member.get("wostatus", []):
        rows.append((
            ws.get("parent") or wonum,
            ws.get("changeby"),
            ws.get("changedate"),
            ws.get("status"),
            laborname
        ))

# Insert data (append new rows)
cursor.executemany("""
INSERT INTO OJT_WO_status (wonum, changeby, changedate, status, laborname)
VALUES (?, ?, ?, ?, ?)
""", rows)

conn.commit()
conn.close()

print(f"Saved {len(rows)} wostatus records to {DB_PATH} in table OJT_WO_status.")

# =============== Get work order info ===================
BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "MXWO"
TABLE_NAME = "OJT_WorkOrder"

where_clause = (
    'woclass="WORKORDER" and '
    'istask=0 and '
    'worktype!="DM" and '
    'zinfowoflag=0 and '
    'status!="CAN" and '
    'assetnum in ["U1UFAC00501","U1UFAC00501-001","U1UFAC00501-002","U1UFAC00501-003","U1UFAC00501-004","U1UFAC00501-005","U1UFAC00501-006"] and '
    'siteid in ["UTIL.GM"] and '
    'ownergroup in ["GM.UT.U"] and '
    f'changedate>"{LAST_REFRESH_DATE}"'
)

params = {
    "oslc.where": where_clause,
    "oslc.select": "wonum,status,description,ownergroup,schedstart,schedfinish,actstart,actfinish,jpnum,assetnum,workorderid",
    "lean": "1"
}

try:
    response = requests.get(
        f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}",
        headers=headers,
        params=params,
        verify=False
    )
    response.raise_for_status()
except requests.RequestException as e:
    print(f"❌ Request failed: {e}")
    exit(1)

print("✅ Request successful.")
members = response.json().get("member", [])

print(f"📦 Received {len(members)} work order records from Maximo.")

# =============== Save to SQLite ===================
print("💾 Preparing to save records to local SQLite DB...")

os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# Create the table if it doesn't exist
print("📄 Creating table (if not exists)...")
cursor.execute(f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        wonum TEXT PRIMARY KEY,
        status TEXT,
        description TEXT,
        ownergroup TEXT,
        schedstart TEXT,
        schedfinish TEXT,
        actstart TEXT,
        actfinish TEXT,
        jpnum TEXT,
        assetnum TEXT,
        workorderid TEXT
    )
""")

print("📝 Inserting records...")
for i, row in enumerate(members, start=1):
    cursor.execute(f"""
        INSERT OR IGNORE INTO {TABLE_NAME} (
            wonum, status, description, ownergroup, schedstart, schedfinish,
            actstart, actfinish, jpnum, assetnum, workorderid
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        row.get("wonum"),
        row.get("status"),
        row.get("description"),
        row.get("ownergroup"),
        row.get("schedstart"),
        row.get("schedfinish"),
        row.get("actstart"),
        row.get("actfinish"),
        row.get("jpnum"),
        row.get("assetnum"),
        row.get("workorderid")
    ))
    if i % 100 == 0:
        print(f"  ➕ Inserted {i} records...")

conn.commit()
conn.close()

# update last refresh date text file
new_refresh_date = datetime.datetime.now(datetime.timezone.utc).replace(microsecond=0).isoformat()
save_last_refresh_date(new_refresh_date)
print(f"🕒 Updated last refresh date to: {new_refresh_date}")

# # =============== INNER JOIN assetnum to equipment description ===================
# Connect to the database
conn = sqlite3.connect("data/Data.db")
cursor = conn.cursor()

cursor.execute("""
DROP TABLE IF EXISTS OJT_raw_data
""")

# Create the new table with joined data
cursor.execute("""
CREATE TABLE IF NOT EXISTS OJT_raw_data AS
SELECT 
    s.wonum,
    s.changeby,
    s.changedate,
    s.status,
    s.laborname,
    w.status AS wo_status,
    w.description,
    w.ownergroup,
    w.schedstart,
    w.schedfinish,
    w.actstart,
    w.actfinish,
    w.jpnum,
    w.assetnum,
    w.workorderid
FROM 
    OJT_WO_status s
INNER JOIN 
    OJT_WorkOrder w
ON 
    s.wonum = w.wonum
""")

conn.commit()
conn.close()

document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('excelFile');
    const uploadBtn = document.getElementById('uploadBtn');
    const downloadBtn = document.getElementById('downloadBtn');
    const uploadStatus = document.getElementById('uploadStatus');
    const workOrderTableBody = document.getElementById('workOrderTableBody');

    // Load work orders when page loads
    loadWorkOrders();

    // File upload handling (only if upload button exists)
    if (uploadBtn) {
        uploadBtn.addEventListener('click', handleFileUpload);
    }

    // Download handling (only if download button exists)
    if (downloadBtn) {
        downloadBtn.addEventListener('click', downloadFlexChart);
    }

    function handleFileUpload() {
        const file = fileInput.files[0];
        if (!file) {
            showStatus('Please select a file first.', 'error');
            return;
        }

        if (!file.name.endsWith('.xlsx')) {
            showStatus('Please select an Excel (.xlsx) file.', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        showStatus('Uploading...', 'info');

        fetch('/api/upload-workorder', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showStatus(data.message, 'success');
                loadWorkOrders();
            } else {
                showStatus('Error: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showStatus('Error: ' + error.message, 'error');
        });
    }

    function downloadFlexChart() {
        downloadBtn.disabled = true;
        showStatus('Downloading Flex Chart...', 'info');
        console.log('Starting download process...');

        fetch('/api/download-flex-chart', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                // Check if we're being redirected to login
                if (response.status === 200 && response.headers.get('content-type')?.includes('text/html')) {
                    throw new Error('Authentication required. Please log in and try again.');
                }
                return response.json().then(data => {
                    throw new Error(data.error || `HTTP error! status: ${response.status}`);
                }).catch(() => {
                    throw new Error(`HTTP error! status: ${response.status}`);
                });
            }

            // Check if response is actually an Excel file
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('spreadsheetml')) {
                throw new Error('Invalid file type received. Please log in and try again.');
            }

            return response.blob();
        })
        .then(blob => {
            console.log('Blob received:', blob);
            console.log('Blob size:', blob.size);
            console.log('Blob type:', blob.type);

            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;

            // Get current date for the filename
            const currentDate = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD
            a.download = `Facility Flex Chart ${currentDate}.xlsx`;

            document.body.appendChild(a);
            a.click();

            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            showStatus('Download completed successfully', 'success');
        })
        .catch(error => {
            console.error('Download error:', error);
            showStatus(`Download failed: ${error.message}`, 'error');
        })
        .finally(() => {
            downloadBtn.disabled = false;
        });
    }

    // Function to load work orders
    function loadWorkOrders() {
        fetch('/api/check-workorders')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    workOrderTableBody.innerHTML = '';
                    data.records.forEach(record => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${record.work_order_number || ''}</td>
                            <td>${record.description || ''}</td>
                            <td>${record.status || ''}</td>
                            <td>${record.status_date || ''}</td>
                            <td>${record.activity_number || ''}</td>
                        `;
                        workOrderTableBody.appendChild(row);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading work orders:', error);
                showStatus('Error loading work orders', 'error');
            });
    }

    // Helper function to show status messages
    function showStatus(message, type) {
        uploadStatus.textContent = message;
        uploadStatus.className = 'status-message show';

        switch(type) {
            case 'success':
                uploadStatus.style.color = 'green';
                uploadStatus.style.backgroundColor = '#d4edda';
                uploadStatus.style.borderColor = '#c3e6cb';
                break;
            case 'error':
                uploadStatus.style.color = 'red';
                uploadStatus.style.backgroundColor = '#f8d7da';
                uploadStatus.style.borderColor = '#f5c6cb';
                break;
            case 'info':
                uploadStatus.style.color = 'blue';
                uploadStatus.style.backgroundColor = '#d1ecf1';
                uploadStatus.style.borderColor = '#bee5eb';
                break;
            default:
                uploadStatus.style.color = 'black';
                uploadStatus.style.backgroundColor = '#f8f9fa';
                uploadStatus.style.borderColor = '#dee2e6';
        }

        // Clear any existing timeout
        if (window.statusTimeout) {
            clearTimeout(window.statusTimeout);
        }

        // Hide the message after 5 seconds with fade out
        window.statusTimeout = setTimeout(() => {
            uploadStatus.className = 'status-message hide';
            // Completely hide after fade out animation
            setTimeout(() => {
                uploadStatus.style.display = 'none';
                uploadStatus.textContent = '';
                uploadStatus.className = 'status-message';
            }, 300);
        }, 5000);
    }

    // Add filter functionality
    document.querySelectorAll('.table-filter').forEach(input => {
        input.addEventListener('input', filterTable);
    });

    function filterTable() {
        const filters = {};
        document.querySelectorAll('.table-filter').forEach(input => {
            filters[input.id] = input.value.toLowerCase();
        });

        const rows = workOrderTableBody.getElementsByTagName('tr');
        Array.from(rows).forEach(row => {
            let showRow = true;
            const cells = row.getElementsByTagName('td');
            Object.keys(filters).forEach((filterId, index) => {
                const cellText = cells[index].textContent.toLowerCase();
                if (!cellText.includes(filters[filterId])) {
                    showRow = false;
                }
            });
            row.style.display = showRow ? '' : 'none';
        });
    }
});

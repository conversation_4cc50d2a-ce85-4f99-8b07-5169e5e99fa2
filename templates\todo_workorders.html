<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TO-DO Work Orders - Ultium Cells</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='sidebar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <!-- Material Icons -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0">
    <!-- DHTMLX Gantt -->
    <script src="https://cdn.dhtmlx.com/gantt/edge/dhtmlxgantt.js"></script>
    <link href="https://cdn.dhtmlx.com/gantt/edge/dhtmlxgantt.css" rel="stylesheet">
    <!-- jsPDF for PDF export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        .container {
            margin-left: 300px;
            padding: 20px;
            transition: margin-left 0.4s ease;
        }

        .container.sidebar-collapsed {
            margin-left: 115px;
        }

        .error-message {
            padding: 20px;
            background-color: #ffebee;
            border: 1px solid #ffcdd2;
            border-radius: 4px;
            color: #b71c1c;
            margin: 20px 0;
            text-align: center;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }

        .page-title {
            color: #333;
            font-size: 28px;
            font-weight: 600;
            margin: 0;
        }

        .filters-container {
            display: flex;
            gap: 15px;
            align-items: center;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-label {
            font-size: 12px;
            font-weight: 600;
            color: #666;
            text-transform: uppercase;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            min-width: 150px;
        }

        #gantt-container {
            height: calc(100vh - 250px);
            width: 100%;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* Status-based task colors */
        .gantt_task_line.status_pending {
            background-color: #FFC107 !important;
        }
        
        .gantt_task_line.status_inprogress {
            background-color: #2196F3 !important;
        }
        
        .gantt_task_line.status_completed {
            background-color: #4CAF50 !important;
        }

        .gantt_task_line.status_overdue {
            background-color: #F44336 !important;
        }

        /* Owner group colors */
        .gantt_task_line.owner_GM_UT_H {
            background-color: #FF6B6B !important;
        }
        
        .gantt_task_line.owner_GM_UT_O {
            background-color: #4ECDC4 !important;
        }
        
        .gantt_task_line.owner_GM_UT_B {
            background-color: #45B7D1 !important;
        }
        
        .gantt_task_line.owner_GM_UT_M {
            background-color: #96CEB4 !important;
        }
        
        .gantt_task_line.owner_GM_UT_E {
            background-color: #FFEAA7 !important;
        }

        .header-buttons {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .refresh-btn, .export-btn {
            padding: 8px 16px;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: background-color 0.2s ease;
        }

        .refresh-btn {
            background: #007bff;
        }

        .refresh-btn:hover {
            background: #0056b3;
        }

        .export-btn {
            background: #28a745;
        }

        .export-btn:hover {
            background: #1e7e34;
        }

        .export-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    {% include 'sidebar.html' %}
    
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">TO-DO Work Orders</h1>
            <div class="header-buttons">
                <button class="export-btn" onclick="exportToPDF()">
                    <span class="material-symbols-rounded">picture_as_pdf</span>
                    Export PDF
                </button>
                <button class="refresh-btn" onclick="loadTodoWorkOrders()">
                    <span class="material-symbols-rounded">refresh</span>
                    Refresh
                </button>
            </div>
        </div>

        <div class="filters-container">
            <div class="filter-group">
                <label class="filter-label">Owner Group</label>
                <select id="ownerGroupFilter" class="filter-select" onchange="applyFilters()">
                    <option value="">All Groups</option>
                    <option value="GM.UT.H">GM.UT.H</option>
                    <option value="GM.UT.O">GM.UT.O</option>
                    <option value="GM.UT.B">GM.UT.B</option>
                    <option value="GM.UT.M">GM.UT.M</option>
                    <option value="GM.UT.E">GM.UT.E</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select id="statusFilter" class="filter-select" onchange="applyFilters()">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="inprogress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="overdue">Overdue</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Date Range</label>
                <input type="date" id="startDateFilter" class="filter-select" onchange="applyFilters()">
            </div>

            <div class="filter-group">
                <label class="filter-label">&nbsp;</label>
                <input type="date" id="endDateFilter" class="filter-select" onchange="applyFilters()">
            </div>
        </div>

        <div id="gantt-container"></div>
    </div>

    <script src="{{ url_for('static', filename='sidebar.js') }}"></script>
    <script src="{{ url_for('static', filename='todo_workorders.js') }}"></script>
</body>
</html>

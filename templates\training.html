<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Training Management</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='sidebar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0">

    <style>
        .status-message {
            display: none;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: 500;
            transition: opacity 0.3s ease-in-out;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }

        .status-message.show {
            display: block;
            opacity: 1;
        }

        .status-message.hide {
            opacity: 0;
        }
    </style>
</head>
<body>
    {% include 'sidebar.html' %}
    <div class="container">
        <h1>Training Management
            <button id="downloadBtn" class="btn-secondary">Download Flex Chart</button>
        </h1>
        <div id="uploadStatus" class="status-message"></div>

        <div class="table-container">
            <h2>Work Orders</h2>
            <table id="workOrderTable">
                <thead>
                    <tr>
                        <th>WO Number</th>
                        <th>Description</th>
                        <th>Status</th>
                        <th>Status Date</th>
                        <th>Activity Number</th>
                    </tr>
                    <tr class="filter-row">
                        <th><input type="text" class="table-filter" id="woNumberFilter" placeholder="Filter WO number..."></th>
                        <th><input type="text" class="table-filter" id="descriptionFilter" placeholder="Filter description..."></th>
                        <th><input type="text" class="table-filter" id="statusFilter" placeholder="Filter status..."></th>
                        <th><input type="text" class="table-filter" id="statusDateFilter" placeholder="Filter date..."></th>
                        <th><input type="text" class="table-filter" id="activityNumberFilter" placeholder="Filter activity number..."></th>
                    </tr>
                </thead>
                <tbody id="workOrderTableBody">
                    <!-- Table rows will be populated dynamically -->
                </tbody>
            </table>
        </div>
    </div>

    <script src="{{ url_for('static', filename='sidebar.js') }}"></script>
    <script src="{{ url_for('static', filename='training.js') }}"></script>
</body>
</html>

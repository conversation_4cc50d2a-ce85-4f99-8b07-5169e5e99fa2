document.addEventListener('DOMContentLoaded', function() {
    let orgData = [];
    let chart = null;
    let userAccessLevel = ''; // Add this to store the user's access level
    let controlsDiv = null;

    // Add a function to fetch the current user's access level
    async function fetchUserAccessLevel() {
        try {
            const response = await fetch('/api/user-access');
            if (!response.ok) {
                throw new Error('Failed to fetch user access level');
            }
            const data = await response.json();
            userAccessLevel = data.access_level;
            console.log('User access level:', userAccessLevel); // Debug log
            return userAccessLevel;
        } catch (error) {
            console.error('Error fetching user access level:', error);
            return '';
        }
    }

    // Function to create and add controls to the page
    function createControls() {
        // Create a container for the chart controls
        const container = document.querySelector('.chart-container');
        controlsDiv = document.createElement('div');

        // Add controls HTML with the current userAccessLevel
        controlsDiv.innerHTML = `
            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                ${userAccessLevel === 'Admin' ? `<button id="modifyToggleBtn" style="background-color: #3498db; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">Modify Chart</button>` : ''}
                <button id="expandAllBtn" style="background-color: #27ae60; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">Expand All</button>
                <button id="collapseAllBtn" style="background-color: #e67e22; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">Collapse All</button>
            </div>
            <div id="addNodeForm" style="display: none; margin-top: 15px;">
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Name:</label>
                    <input id="nodeName" type="text" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Team:</label>
                    <select id="nodeTeam" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="">Select Team</option>
                        <option value="Technician">Technician</option>
                        <option value="HVAC">HVAC</option>
                        <option value="Operation">Operation</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Title:</label>
                    <input id="nodeTitle" type="text" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Position:</label>
                    <input id="nodePosition" type="text" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Department:</label>
                    <select id="nodeDepartment" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="">Select a department...</option>
                        <option value="Facility Maintenance">Facility Maintenance</option>
                    </select>
                </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Shift:</label>
                    <select id="nodeShift" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="">Select Shift</option>
                        <option value="Day Shift">Day Shift</option>
                        <option value="Night Shift">Night Shift</option>
                        <option value="A-Crew">A-Crew</option>
                        <option value="B-Crew">B-Crew</option>
                        <option value="C-Crew">C-Crew</option>
                        <option value="D-Crew">D-Crew</option>
                        <option value="UAW">UAW</option>
                    </select>
                </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Phone:</label>
                    <input id="nodePhone" type="text" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" placeholder="(*************">
                </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Image URL (optional):</label>
                    <input id="nodeImage" type="text" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" placeholder="https://example.com/image.jpg">
                    <small style="color: #7f8c8d; display: block; margin-top: 4px;">Leave empty to use initials</small>
                </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Reports To:</label>
                    <select id="nodeParent" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <!-- Will be populated dynamically -->
                    </select>
                </div>
                <button id="saveNodeBtn" style="background-color: #2ecc71; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">Save</button>
                <button id="cancelNodeBtn" style="background-color: #95a5a6; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-left: 10px;">Cancel</button>
            </div>
        `;

        // Insert the controls before the chart
        container.insertBefore(controlsDiv, container.firstChild);

        // Create a div for the actual chart
        const chartDiv = document.createElement('div');
        chartDiv.id = 'org-chart';
        chartDiv.style.height = '700px';
        container.appendChild(chartDiv);

        // Set up event listeners for the controls
        setupControlEventListeners();
    }

    // Fetch organization data from the server
    async function fetchOrgData() {
        // First fetch the user's access level
        await fetchUserAccessLevel();

        // Now create the controls with the correct access level
        createControls();

        try {
            const response = await fetch('/api/org-chart');
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            orgData = await response.json();

            // Check if we have data
            if (!orgData || orgData.length === 0) {
                console.error('No organization data received from server');
                document.getElementById('org-chart').innerHTML = '<div style="padding: 20px; text-align: center;"><h3>No organization data available</h3><p>Please add employees to the organization chart.</p></div>';
                return;
            }

            // Check if root node exists (ID: 1)
            const rootNode = orgData.find(node => node.id === '1');
            if (!rootNode) {
                console.error('Root node (ID: 1) is missing');
                // Add a temporary root node
                orgData.unshift({
                    id: '1',
                    parentId: '',
                    name: 'Ultium Cells Facility',
                    title: '',
                    position: '',
                    department: '',
                    team: '',
                    shift: '',
                    phone: '',
                    image: 'https://media.licdn.com/dms/image/v2/C560BAQEYq3rogDbBdQ/company-logo_200_200/company-logo_200_200/0/1654712591371/ultium_cells_logo?e=2147483647&v=beta&t=FvNgPTg8dIubA0R0LiFpEybEFApHHzA2lMoX2UHJK2s'
                });
                console.log('Added temporary root node');
            }

            // Check for orphaned nodes and fix them
            const nodeIds = new Set(orgData.map(node => node.id));
            let orphanedNodes = 0;

            orgData.forEach(node => {
                if (node.parentId && !nodeIds.has(node.parentId)) {
                    console.warn(`Node ${node.id} (${node.name}) has non-existent parent ID: ${node.parentId}`);
                    // Fix by setting parentId to root
                    node.parentId = '1';
                    orphanedNodes++;
                }
            });

            if (orphanedNodes > 0) {
                console.log(`Fixed ${orphanedNodes} orphaned nodes by setting their parentId to '1'`);
            }

            chart = renderChart(); // Assign to the existing chart variable
        } catch (error) {
            console.error('Error fetching org chart data:', error);
            document.getElementById('org-chart').innerHTML = `<div style="padding: 20px; text-align: center;"><h3>Error loading organization chart</h3><p>${error.message}</p></div>`;
        }
    }

    // Save organization data to the server
    async function saveOrgData(data) {
        try {
            const response = await fetch('/api/org-chart', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return await response.json();
        } catch (error) {
            console.error('Error saving org chart data:', error);
            return null;
        }
    }

    // Track if we're in modify mode
    let isModifyMode = false;

    // Function to reattach event listeners to node buttons (edit, add, remove)
    function reattachNodeButtonEventListeners() {
        console.log("Reattaching node button event listeners");
        document.querySelectorAll('.edit-btn, .add-btn, .remove-btn').forEach(btn => {
            btn.style.pointerEvents = 'auto';

            const nodeId = btn.getAttribute('data-node-id');
            const newBtn = btn.cloneNode(true);
            btn.parentNode.replaceChild(newBtn, btn);

            if (newBtn.classList.contains('edit-btn')) {
                newBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    console.log("Edit button clicked for node:", nodeId);
                    editNodeHandler(nodeId);
                });
            } else if (newBtn.classList.contains('add-btn')) {
                newBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    console.log("Add button clicked for node:", nodeId);
                    addNodeHandler(nodeId);
                });
            } else if (newBtn.classList.contains('remove-btn')) {
                newBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    console.log("Remove button clicked for node:", nodeId);
                    removeNodeHandler(nodeId);
                });
            }
        });

        console.log("Node button event listeners reattached");
    }

    // Function to set up event listeners for the controls
    function setupControlEventListeners() {
        // Toggle modify mode - only attach if the button exists
        const modifyToggleBtn = document.getElementById('modifyToggleBtn');
        if (modifyToggleBtn) {
            modifyToggleBtn.addEventListener('click', function() {
                isModifyMode = !isModifyMode;
                this.textContent = isModifyMode ? 'Exit Modify Mode' : 'Modify Chart';
                this.style.backgroundColor = isModifyMode ? '#e74c3c' : '#3498db';
                chart = renderChart();
            });
        } else {
            // Ensure modify mode is always false for non-admin users
            isModifyMode = false;
        }

        // Expand all nodes
        document.getElementById('expandAllBtn').addEventListener('click', function() {
            if (chart) {
                chart.expandAll();
                console.log('Expanded all nodes');

                // Reattach event listeners after expanding
                if (isModifyMode) {
                    setTimeout(() => {
                        reattachNodeButtonEventListeners();
                    }, 500);
                }
            } else {
                console.error('Chart not initialized');
            }
        });

        // Collapse all nodes
        document.getElementById('collapseAllBtn').addEventListener('click', function() {
            if (chart) {
                chart.collapseAll();
                console.log('Collapsed all nodes');

                // Reattach event listeners after collapsing
                if (isModifyMode) {
                    setTimeout(() => {
                        reattachNodeButtonEventListeners();
                    }, 500);
                }
            } else {
                console.error('Chart not initialized');
            }
        });

        // Cancel button click event
        document.getElementById('cancelNodeBtn').addEventListener('click', function() {
            document.getElementById('addNodeForm').style.display = 'none';
            document.getElementById('addNodeForm').removeAttribute('data-editing-id');
            document.getElementById('saveNodeBtn').textContent = 'Save';
        });

        // Save button click event
        document.getElementById('saveNodeBtn').addEventListener('click', async function() {
            const name = document.getElementById('nodeName').value;
            const title = document.getElementById('nodeTitle').value;
            const position = document.getElementById('nodePosition').value;
            const department = document.getElementById('nodeDepartment').value;
            const team = document.getElementById('nodeTeam').value;
            const shift = document.getElementById('nodeShift').value;
            const phone = document.getElementById('nodePhone').value;
            const imageUrl = document.getElementById('nodeImage').value.trim();
            const parentId = document.getElementById('nodeParent').value;
            const editingId = document.getElementById('addNodeForm').getAttribute('data-editing-id');

            if (!name) {
                alert('Please enter a name');
                return;
            }

            // Store the current expanded/collapsed state before updating
            let expandedState = {};
            if (chart) {
                chart.getChartState().allNodes.forEach(node => {
                    expandedState[node.data.id] = node.data._expanded !== false;
                });
            }

            if (editingId) {
                // Update existing node
                const nodeIndex = orgData.findIndex(node => node.id === editingId);
                if (nodeIndex !== -1) {
                    orgData[nodeIndex] = {
                        ...orgData[nodeIndex],
                        name,
                        title,
                        position,
                        department,
                        team,
                        shift,
                        phone,
                        parentId: parentId || '',
                        image: imageUrl
                    };
                }
            } else {
                // Create a new node
                const newNode = {
                    id: (Math.max(...orgData.map(node => parseInt(node.id))) + 1).toString(),
                    parentId: parentId || '',
                    name,
                    title,
                    position,
                    department,
                    team,
                    shift,
                    phone,
                    image: imageUrl
                };
                orgData.push(newNode);
            }

            // Save to server
            const result = await saveOrgData(orgData);
            if (result && result.success) {
                // Reset form
                document.getElementById('nodeName').value = '';
                document.getElementById('nodeTitle').value = '';
                document.getElementById('nodePosition').value = '';
                document.getElementById('nodeDepartment').value = '';
                document.getElementById('nodeTeam').value = '';
                document.getElementById('nodeShift').value = '';
                document.getElementById('nodePhone').value = '';
                document.getElementById('nodeImage').value = '';
                document.getElementById('nodeParent').value = '';
                document.getElementById('addNodeForm').style.display = 'none';
                document.getElementById('addNodeForm').removeAttribute('data-editing-id');
                document.getElementById('saveNodeBtn').textContent = 'Save';

                // Update orgData without re-rendering
                const updatedData = await fetchOrgDataWithoutRender();
                if (updatedData) {
                    orgData = updatedData;

                    // Apply the expanded state to the updated data
                    orgData.forEach(node => {
                        if (expandedState[node.id] !== undefined) {
                            node._expanded = expandedState[node.id];
                        }
                    });

                    // Force modify mode to stay active
                    isModifyMode = true;
                    document.getElementById('modifyToggleBtn').textContent = 'Exit Modify Mode';
                    document.getElementById('modifyToggleBtn').style.backgroundColor = '#e74c3c';

                    // Update the chart with the new data while preserving expanded state
                    if (chart) {
                        chart.data(orgData);
                        chart.render();

                        // Reattach event listeners after rendering
                        setTimeout(() => {
                            reattachNodeButtonEventListeners();
                            console.log("Event listeners reattached after update");
                        }, 1000);
                    }
                }
            }
        });
    }

    // Function to render the chart
    function renderChart() {
        try {
            const newChart = new d3.OrgChart()
                .nodeHeight(() => 120)
                .nodeWidth(() => 240)
            .childrenMargin(() => 50)
            .compactMarginBetween(() => 35)
            .compactMarginPair(() => 30)
            .neighbourMargin(() => 20)
            .nodeContent((d) => {
                // Determine background color based on shift
                let color = '#FFFFFF'; // Default white background

                // Get shift value and determine color
                const shift = d.data.shift || '';
                if (shift.includes('A-Crew')) {
                    color = '#ffebee'; // Light red for A-Crew
                } else if (shift.includes('B-Crew')) {
                    color = '#e8f5e9'; // Light green for B-Crew
                } else if (shift.includes('C-Crew')) {
                    color = '#e3f2fd'; // Light blue for C-Crew
                } else if (shift.includes('D-Crew')) {
                    color = '#fffde7'; // Light yellow for D-Crew
                } else if (shift.includes('Day Shift')) {
                    color = '#f3e5f5'; // Light purple for Day Shift
                } else if (shift.includes('Night Shift')) {
                    color = '#e0f2f1'; // Light teal for Night Shift
                } else if (shift.includes('UAW')) {
                    color = '#fff3e0'; // Light orange for UAW
                }

                // Create modify buttons if in modify mode
                const modifyButtons = isModifyMode ? `
                    <div style="position: absolute; top: 5px; right: 5px; display: flex; gap: 5px; z-index: 10;">
                        <button class="edit-btn" data-node-id="${d.data.id}" style="background-color: #f39c12; color: white; border: none; width: 24px; height: 24px; border-radius: 50%; font-size: 14px; line-height: 1; cursor: pointer; display: flex; align-items: center; justify-content: center;">✎</button>
                        <button class="add-btn" data-node-id="${d.data.id}" style="background-color: #2ecc71; color: white; border: none; width: 24px; height: 24px; border-radius: 50%; font-size: 16px; line-height: 1; cursor: pointer; display: flex; align-items: center; justify-content: center;">+</button>
                        ${d.data.id !== "1" ? `<button class="remove-btn" data-node-id="${d.data.id}" style="background-color: #e74c3c; color: white; border: none; width: 24px; height: 24px; border-radius: 50%; font-size: 16px; line-height: 1; cursor: pointer; display: flex; align-items: center; justify-content: center;">×</button>` : ''}
                    </div>` : '';

                // Use a default image if the image URL is from the external CDN or missing
                let imageHtml = '';
                if (d.data.image && !d.data.image.includes('cdn.balkan.app')) {
                    imageHtml = `<img src="${d.data.image}" style="width: 50px; height: 50px; border-radius: 50%; border: 2px solid #E4E2E9; background-color: #F9F9F9;" onerror="this.style.display='none'">`;
                } else {
                    // Use initials instead of image
                    const initials = d.data.name ? d.data.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase() : '??';
                    imageHtml = `<div style="width: 50px; height: 50px; border-radius: 50%; border: 2px solid #E4E2E9; background-color: #3498db; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold;">${initials}</div>`;
                }

                // Create a left border with a more prominent color based on shift
                let borderColor = '#E4E2E9'; // Default border color
                let shiftIndicator = '';

                if (shift.includes('A-Crew')) {
                    borderColor = '#ffcdd2'; // Stronger red for A-Crew
                    shiftIndicator = '<div style="position: absolute; top: 0; left: 0; width: 5px; height: 100%; background-color: #ef5350; border-top-left-radius: 10px; border-bottom-left-radius: 10px;"></div>';
                } else if (shift.includes('B-Crew')) {
                    borderColor = '#c8e6c9'; // Stronger green for B-Crew
                    shiftIndicator = '<div style="position: absolute; top: 0; left: 0; width: 5px; height: 100%; background-color: #66bb6a; border-top-left-radius: 10px; border-bottom-left-radius: 10px;"></div>';
                } else if (shift.includes('C-Crew')) {
                    borderColor = '#bbdefb'; // Stronger blue for C-Crew
                    shiftIndicator = '<div style="position: absolute; top: 0; left: 0; width: 5px; height: 100%; background-color: #42a5f5; border-top-left-radius: 10px; border-bottom-left-radius: 10px;"></div>';
                } else if (shift.includes('D-Crew')) {
                    borderColor = '#fff9c4'; // Stronger yellow for D-Crew
                    shiftIndicator = '<div style="position: absolute; top: 0; left: 0; width: 5px; height: 100%; background-color: #ffee58; border-top-left-radius: 10px; border-bottom-left-radius: 10px;"></div>';
                } else if (shift.includes('Day Shift')) {
                    borderColor = '#ab47bc'; // Stronger purple for Day Shift
                    shiftIndicator = '<div style="position: absolute; top: 0; left: 0; width: 5px; height: 100%; background-color: #ab47bc; border-top-left-radius: 10px; border-bottom-left-radius: 10px;"></div>';
                } else if (shift.includes('Night Shift')) {
                    borderColor = '#26a69a'; // Stronger teal for Night Shift
                    shiftIndicator = '<div style="position: absolute; top: 0; left: 0; width: 5px; height: 100%; background-color: #26a69a; border-top-left-radius: 10px; border-bottom-left-radius: 10px;"></div>';
                } else if (shift.includes('UAW')) {
                    borderColor = '#ffa726'; // Stronger orange for UAW
                    shiftIndicator = '<div style="position: absolute; top: 0; left: 0; width: 5px; height: 100%; background-color: #ffa726; border-top-left-radius: 10px; border-bottom-left-radius: 10px;"></div>';
                }

                return `
                    <div style="background-color: ${color}; margin-left: 1px; height: 110px; width: 240px; border-radius: 10px; border: 1px solid ${borderColor}; position: relative; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        ${shiftIndicator}
                        ${modifyButtons}
                        <div style="display: flex; padding: 12px; height: 100%; position: relative; z-index: 1;">
                            <div style="display: flex; align-items: center; margin-right: 12px;">
                                ${imageHtml}
                            </div>
                            <div style="display: flex; flex-direction: column; justify-content: center; text-align: left; flex: 1; overflow: hidden;">
                                <div style="font-size: 14px; font-weight: bold; color: #2D3436; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-bottom: -2px;">${d.data.name}</div>
                                <div style="font-size: 12px; color: #3498db; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; top-padding: 2px; margin-bottom: 3px;">${d.data.position || ''}</div>
                                <div style="font-size: 11px; color: #7f8c8d; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-bottom: 3px;">${d.data.title || ''}</div>
                                ${d.data.shift ? `<div style="font-size: 10px; color: #95a5a6; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-bottom: 2px;">Shift: ${d.data.shift}</div>` : ''}
                                ${d.data.phone ? `<div style="font-size: 10px; color: #95a5a6; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">${d.data.phone}</div>` : ''}
                            </div>
                        </div>
                    </div>`;
            });

        // Set container and data
        newChart
            .container('#org-chart')
            .data(orgData);

        // Configure chart to always show buttons for nodes with children
        newChart
            .linkUpdate(function() {
                d3.select(this)
                    .attr("stroke", "#E4E2E9")
                    .attr("stroke-width", 1);
            });

        // Add event listeners for expanding/collapsing nodes with arrows and node count
        newChart
            .buttonContent(({ node }) => {
                // Find direct children in the original data
                const nodeId = node.data.id;
                const directChildren = orgData.filter(item => item.parentId === nodeId);

                // Only show button if there are direct children
                if (directChildren.length === 0) return '';

                // Show arrow and count
                return `
                    <div style="color:#716E7B;border-radius:5px;padding:4px 8px;font-size:12px;margin:auto auto;background-color:#F3F2F9;border: 1px solid #E4E2E9;display:flex;align-items:center;justify-content:center;gap:5px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);cursor:pointer;">
                        <span style="font-weight:bold;color:#3498db;">${directChildren.length}</span>
                        <span style="transform:rotate(${node.children ? '180deg' : '0deg'});transition:transform 0.3s ease;font-size:16px;color:#2c3e50;">↓</span>
                    </div>
                `;
            })
            .nodeUpdate(function(d) {
                // Make sure expand/collapse buttons are visible for all nodes with children
                const nodeId = d.data.id;
                const hasChildren = orgData.some(item => item.parentId === nodeId);

                if (hasChildren) {
                    d3.select(this).select('.node-button')
                        .style('visibility', 'visible');
                }
            });

        newChart.render();

        // Make sure to attach event listeners after the chart is rendered
        if (isModifyMode) {
            setTimeout(() => {
                reattachNodeButtonEventListeners();
                console.log("Event listeners attached after render");
            }, 1000);
        }

        return newChart;
    } catch (error) {
        console.error('Error rendering chart:', error);
        document.getElementById('org-chart').innerHTML = `<div style="padding: 20px; text-align: center;"><h3>Error rendering organization chart</h3><p>${error.message}</p></div>`;
        return null;
    }
}

    // Function to populate the parent dropdown
    function populateParentDropdown(excludeId = null) {
        const parentSelect = document.getElementById('nodeParent');
        parentSelect.innerHTML = '<option value="">No Parent (Root)</option>';

        orgData.forEach(node => {
            if (node.id !== excludeId) {  // Don't include the node being edited as a potential parent
                const option = document.createElement('option');
                option.value = node.id;
                option.textContent = `${node.name} (${node.position})`;
                parentSelect.appendChild(option);
            }
        });
    }

    // Define handler functions
    function editNodeHandler(nodeId) {
        const nodeToEdit = orgData.find(node => node.id === nodeId);
        if (!nodeToEdit) {
            console.error("Node not found:", nodeId);
            return;
        }

        document.getElementById('addNodeForm').style.display = 'block';
        document.getElementById('nodeName').value = nodeToEdit.name || '';
        document.getElementById('nodeTitle').value = nodeToEdit.title || '';
        document.getElementById('nodePosition').value = nodeToEdit.position || '';
        document.getElementById('nodeDepartment').value = nodeToEdit.department || '';
        document.getElementById('nodeTeam').value = nodeToEdit.team || '';
        document.getElementById('nodeShift').value = nodeToEdit.shift || '';
        document.getElementById('nodePhone').value = nodeToEdit.phone || '';
        document.getElementById('nodeImage').value = nodeToEdit.image || '';

        populateParentDropdown(nodeId);
        document.getElementById('nodeParent').value = nodeToEdit.parentId || '';

        document.getElementById('addNodeForm').setAttribute('data-editing-id', nodeId);
        document.getElementById('saveNodeBtn').textContent = 'Update';
    }

    function addNodeHandler(parentId) {
        document.getElementById('addNodeForm').style.display = 'block';
        document.getElementById('addNodeForm').removeAttribute('data-editing-id');
        document.getElementById('saveNodeBtn').textContent = 'Save';

        // Clear form fields
        document.getElementById('nodeName').value = '';
        document.getElementById('nodeTitle').value = '';
        document.getElementById('nodePosition').value = '';
        document.getElementById('nodeDepartment').value = '';
        document.getElementById('nodeTeam').value = '';
        document.getElementById('nodeShift').value = '';
        document.getElementById('nodePhone').value = '';
        document.getElementById('nodeImage').value = '';

        // Set parent
        populateParentDropdown();
        document.getElementById('nodeParent').value = parentId;
    }

    function removeNodeHandler(nodeId) {
        const nodeToRemove = orgData.find(node => node.id === nodeId);
        if (!nodeToRemove) {
            console.error("Node not found:", nodeId);
            return;
        }

        // Store the current expanded/collapsed state before updating
        let expandedState = {};
        if (chart) {
            chart.getChartState().allNodes.forEach(node => {
                if (node.data.id !== nodeId) { // Don't store state for the node being removed
                    expandedState[node.data.id] = node.data._expanded !== false;
                }
            });
        }

        // Find all descendants (direct and indirect reports)
        const allDescendants = findAllChildren(nodeId);

        let confirmMessage = `Are you sure you want to remove ${nodeToRemove.name}?`;

        // If there are any descendants, warn the user
        if (allDescendants.length > 0) {
            const descendantNodes = allDescendants.map(id => orgData.find(node => node.id === id));
            const descendantNames = descendantNodes.map(node => node.name).join(", ");
            confirmMessage += `\n\nWARNING: This will also remove all employees under them (${allDescendants.length} total):\n${descendantNames}`;
        }

        if (confirm(confirmMessage)) {
            // Remove the node and all its descendants from orgData
            const idsToRemove = [nodeId, ...allDescendants];
            orgData = orgData.filter(node => !idsToRemove.includes(node.id));
            saveOrgData(orgData).then(async () => {
                // Update orgData without re-rendering
                const updatedData = await fetchOrgDataWithoutRender();
                if (updatedData) {
                    orgData = updatedData;

                    // Apply the expanded state to the updated data
                    orgData.forEach(node => {
                        if (expandedState[node.id] !== undefined) {
                            node._expanded = expandedState[node.id];
                        }
                    });

                    // Force modify mode to stay active
                    isModifyMode = true;
                    document.getElementById('modifyToggleBtn').textContent = 'Exit Modify Mode';
                    document.getElementById('modifyToggleBtn').style.backgroundColor = '#e74c3c';

                    // Update the chart with the new data while preserving expanded state
                    if (chart) {
                        chart.data(orgData);
                        chart.render();

                        // Reattach event listeners after rendering
                        setTimeout(() => {
                            reattachNodeButtonEventListeners();
                            console.log("Event listeners reattached after removal");
                        }, 1000);
                    }
                }
            });
        }
    }

    // Function to find all children of a node
    function findAllChildren(nodeId) {
        const childrenIds = [];

        function findChildren(id) {
            const children = orgData.filter(node => node.parentId === id);
            children.forEach(child => {
                childrenIds.push(child.id);
                findChildren(child.id);
            });
        }

        findChildren(nodeId);
        return childrenIds;
    }

    // Initial load of data - this will fetch the user access level first,
    // then create the controls with the correct access level, and finally load the chart
    fetchOrgData();
});

// Add a new function to fetch data without rendering
async function fetchOrgDataWithoutRender() {
    try {
        const response = await fetch('/api/org-chart');
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const data = await response.json();

        // Check if we have data
        if (!data || data.length === 0) {
            console.error('No organization data received from server');
            return null;
        }

        // Check if root node exists (ID: 1)
        const rootNode = data.find(node => node.id === '1');
        if (!rootNode) {
            console.error('Root node (ID: 1) is missing');
            // Add a temporary root node
            data.unshift({
                id: '1',
                parentId: '',
                name: 'Ultium Cells Facility',
                title: '',
                position: '',
                department: '',
                team: '',
                shift: '',
                phone: '',
                image: 'https://media.licdn.com/dms/image/v2/C560BAQEYq3rogDbBdQ/company-logo_200_200/company-logo_200_200/0/1654712591371/ultium_cells_logo?e=2147483647&v=beta&t=FvNgPTg8dIubA0R0LiFpEybEFApHHzA2lMoX2UHJK2s'
            });
            console.log('Added temporary root node');
        }

        // Check for orphaned nodes and fix them
        const nodeIds = new Set(data.map(node => node.id));
        let orphanedNodes = 0;

        data.forEach(node => {
            if (node.parentId && !nodeIds.has(node.parentId)) {
                console.warn(`Node ${node.id} (${node.name}) has non-existent parent ID: ${node.parentId}`);
                // Fix by setting parentId to root
                node.parentId = '1';
                orphanedNodes++;
            }
        });

        if (orphanedNodes > 0) {
            console.log(`Fixed ${orphanedNodes} orphaned nodes by setting their parentId to '1'`);
        }

        return data;
    } catch (error) {
        console.error('Error fetching org chart data:', error);
        return null;
    }
}

// Add download button event listener
document.getElementById('downloadOrgChart').addEventListener('click', async function() {
    const button = this;
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '<span class="material-symbols-rounded">hourglass_empty</span> Exporting...';
    button.disabled = true;

    try {
        console.log('Starting OrgChart export...');

        // Call the export endpoint
        const response = await fetch('/api/export-orgchart', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (!response.ok) {
            throw new Error(`Export failed: ${response.status} ${response.statusText}`);
        }

        // Get the blob from the response
        const blob = await response.blob();

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'OrgChart_Export.xlsx';

        // Trigger download
        document.body.appendChild(a);
        a.click();

        // Cleanup
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        console.log('OrgChart export completed successfully');

        // Show success state briefly
        button.innerHTML = '<span class="material-symbols-rounded">check_circle</span> Exported!';
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 2000);

    } catch (error) {
        console.error('Error exporting OrgChart:', error);

        // Show error state
        button.innerHTML = '<span class="material-symbols-rounded">error</span> Export Failed';
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 3000);
    }
});

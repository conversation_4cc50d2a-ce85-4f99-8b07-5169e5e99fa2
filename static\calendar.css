/* Calendar Container Styles */
.container {
    margin-left: 300px;
    padding: 20px;
    transition: margin-left 0.4s ease;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.container.sidebar-collapsed {
    margin-left: 115px;
}

/* Calendar Header */
.calendar-header {
    background: white;
    padding: 20px;
    border-radius: 10px 10px 0 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.calendar-title h1 {
    margin: 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.calendar-controls {
    display: flex;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
}

/* View Toggle Buttons */
.view-toggle {
    display: flex;
    background: #f0f0f0;
    border-radius: 8px;
    padding: 4px;
    gap: 2px;
}

.view-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.2s ease;
}

.view-btn:hover {
    background: #e0e0e0;
    color: #333;
}

.view-btn.active {
    background: #007bff;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.view-btn .material-symbols-rounded {
    font-size: 18px;
}

/* Navigation Controls */
.navigation-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
}

.nav-btn .material-symbols-rounded {
    font-size: 20px;
    color: #666;
}

.date-picker {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    background: white;
    min-width: 150px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.date-picker:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.date-picker:hover {
    border-color: #007bff;
}

.today-btn {
    padding: 10px 16px;
    border: 1px solid #007bff;
    background: white;
    color: #007bff;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.today-btn:hover {
    background: #007bff;
    color: white;
}

/* Calendar Container */
.calendar-container {
    background: white;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: calc(100vh - 200px);
    overflow: hidden;
}

.calendar-view {
    height: 100%;
    padding: 20px;
}

/* List View Styles */
.list-view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.list-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
}

.list-filters {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-select, .filter-date {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    min-width: 150px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-select:focus, .filter-date:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.filter-select:hover, .filter-date:hover {
    border-color: #007bff;
}

.clear-filters-btn {
    padding: 8px 16px;
    border: 1px solid #dc3545;
    background: white;
    color: #dc3545;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.clear-filters-btn:hover {
    background: #dc3545;
    color: white;
}

.list-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* Work Order Table */
.work-order-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.work-order-table th {
    background: #f8f9fa;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #dee2e6;
    font-size: 14px;
}

.work-order-table td {
    padding: 12px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    color: #555;
}

.work-order-table tr:hover {
    background: #f8f9fa;
    cursor: pointer;
}

.work-order-table tr:nth-child(even) {
    background: #fafafa;
}

.work-order-table tr:nth-child(even):hover {
    background: #f0f0f0;
}

/* Status badges in table */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-inprogress {
    background-color: #cce7ff;
    color: #004085;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

/* DayPilot Calendar Customizations */
#dp {
    height: 100% !important;
}

/* Event styling for calendar views */
.calendar_default_event_inner {
    border-radius: 4px !important;
    padding: 4px 8px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-shadow: none !important;
}

/* Calendar cell styling */
.calendar_white_cell {
    border: 1px solid #e9ecef !important;
}

.calendar_white_cell_business {
    background-color: #ffffff !important;
}

.calendar_white_cell_weekend {
    background-color: #f8f9fa !important;
}

/* Month view header styling */
.calendar_white_header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    text-align: center !important;
    padding: 10px !important;
    border: none !important;
}

/* Week/Day view time column styling */
.calendar_white_hour {
    background-color: #f8f9fa !important;
    border-right: 2px solid #dee2e6 !important;
    font-weight: 500 !important;
    color: #495057 !important;
}

/* Today highlighting */
.calendar_white_cell_today {
    background-color: #e3f2fd !important;
    border: 2px solid #2196f3 !important;
}

/* Event hover effects */
.calendar_default_event:hover .calendar_default_event_inner {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-1px) !important;
    transition: all 0.2s ease !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .calendar-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .calendar-controls {
        justify-content: space-between;
        gap: 15px;
    }
    
    .view-toggle {
        order: 2;
    }
    
    .navigation-controls {
        order: 1;
        justify-content: center;
    }
    
    .list-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-select, .filter-date {
        min-width: auto;
    }
}

/* Hide DayPilot default orange selection */
div[unselectable="on"][style*="background-color: rgb(255, 102, 0)"] {
    display: none !important;
}

/* Event Details Modal */
.event-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
    border-radius: 10px 10px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #e9ecef;
    color: #333;
}

.modal-body {
    padding: 20px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row label {
    font-weight: 600;
    color: #333;
    min-width: 120px;
}

.detail-row span {
    color: #555;
    text-align: right;
    flex: 1;
}

/* Loading state */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced table styling for better MAQ-like appearance */
.work-order-table {
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.work-order-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 13px;
}

.work-order-table td {
    vertical-align: middle;
    transition: all 0.2s ease;
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state .material-symbols-rounded {
    font-size: 48px;
    color: #dee2e6;
    margin-bottom: 16px;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 500;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

.calendar_white_event.outline-event {
    border: 4px solid var(--event-border-color, #000000) !important;
    color: var(--event-border-color, #000000) !important;
    background-color: transparent !important;
    box-sizing: border-box;
    white-space: pre-wrap;
}

.auto-height-event {
    min-height: 60px !important;
    height: auto !important;
}

.auto-height-event .calendar_default_event_inner {
    height: auto !important;
    overflow: visible !important;
}
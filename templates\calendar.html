<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendar - Ultium Cells</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='sidebar.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0">
    <!-- jQuery library -->
    <script src="https://code.jquery.com/jquery-1.9.1.min.js"></script>
    <!-- DayPilot Pro CSS and JS -->
    <link type="text/css" rel="stylesheet" href="{{ url_for('static', filename='daypilot/calendar_white.css') }}" />
    <script src="{{ url_for('static', filename='daypilot/daypilot-all.min.js') }}"></script>

    <style>
        .container {
            margin-left: 300px;
            padding: 20px;
            transition: margin-left 0.4s ease;
            height: 100vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .container.sidebar-collapsed {
            margin-left: 115px;
        }

        /* Calendar Header */
        .calendar-header {
            background: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0;
            flex-shrink: 0;
        }

        .calendar-title h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }

        /* Navigation Controls */
        .navigation-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            transition: all 0.2s ease;
        }

        .nav-btn:hover {
            background: #f0f0f0;
            border-color: #ccc;
        }

        .today-btn {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .today-btn:hover {
            background: #218838;
            border-color: #1e7e34;
        }

        .date-picker {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
        }

        /* View Toggle Buttons */
        .view-toggle {
            display: flex;
            background: #f0f0f0;
            border-radius: 8px;
            padding: 4px;
            gap: 2px;
        }

        .view-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.2s ease;
        }

        .view-btn:hover {
            background: #e0e0e0;
            color: #333;
        }

        .view-btn.active {
            background: "transparent";
            color: white;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        }

        .calendar-container {
            background: white;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            flex: 1;
            overflow: auto;
            min-height: 0;
        }

        #dp {
            height: 100%;
            min-height: 600px;
        }

        /* Status-based colors */
        .status-completed {
            background-color: #4CAF50 !important;
        }

        .status-inprogress {
            background-color: #2196F3 !important;
        }

        .status-pending {
            background-color: #FFC107 !important;
        }

        /* Hide DayPilot default orange selection and DEMO text */
        div[unselectable="on"][style*="background-color: rgb(255, 102, 0)"] {
            display: none !important;
        }

        /* Hide DEMO watermark */
        div[style*="background-color: rgb(255, 102, 0)"][style*="color: white"] {
            display: none !important;
        }

        .refresh-btn, .export-btn {
            padding: 8px 16px;
            color: rgb(11, 166, 238);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: background-color 0.2s ease;
        }

        /* Fix week view column headers to show full day names */
        .calendar_default_colheader_inner {
            white-space: nowrap !important;
            overflow: visible !important;
            text-overflow: clip !important;
            min-width: 80px !important;
            font-size: 12px !important;
        }

        .calendar_default_colheader {
            min-width: 80px !important;
        }

        /* Highlight today's date with gray shade across all views */

        /* Month view - today's cell */
        .calendar_white_cell.calendar_white_cell_today,
        .calendar_default_cell.calendar_default_cell_today {
            background-color: #f0f0f0 !important;
        }

        /* Week view - today's column */
        .calendar_default_matrix_horizontal_line.calendar_default_matrix_horizontal_line_today,
        .calendar_default_cell.calendar_default_cell_today {
            background-color: #f5f5f5 !important;
        }

        /* Day view - today's header and cells */
        .calendar_default_colheader.calendar_default_colheader_today {
            background-color: #e8e8e8 !important;
            font-weight: bold !important;
        }

        /* Today's date in month view - date number styling */
        .calendar_white_cell_today .calendar_white_cell_text,
        .calendar_default_cell_today .calendar_default_cell_text {
            background-color: #d0d0d0 !important;
            color: #333 !important;
            font-weight: bold !important;
            border-radius: 3px !important;
            padding: 2px 4px !important;
        }

        /* Additional today highlighting for different DayPilot themes */
        div[class*="today"] {
            background-color: #8d3333 !important;
        }

        /* Today's time slots in week/day view */
        .calendar_default_cell[data-date*="today"],
        .calendar_default_cell.today {
            background-color: #f8f8f8 !important;
        }


    </style>
</head>
<body>
    {% include 'sidebar.html' %}

    <div class="container">
        <!-- Calendar Header with Navigation and View Toggle -->
        <div class="calendar-header">
            <div class="calendar-title">
                <h1>Calendar</h1>
            </div>
            <button class="export-btn" onclick="exportToPDF()">
                <span class="material-symbols-rounded">picture_as_pdf</span>
                Export PDF
            </button>

            <div class="navigation-controls">
                <button class="nav-btn" id="prevBtn">‹</button>
                <input type="date" class="date-picker" id="datePicker">
                <button class="nav-btn today-btn" id="todayBtn">Today</button>
                <button class="nav-btn" id="nextBtn">›</button>
            </div>

            <!-- Crew Filter Dropdown -->
            <div class="crew-filter">
                <div class="crew-filter-toggle" onclick="toggleCrewFilter()">
                    <span class="crew-filter-summary" id="crewFilterSummary"></span>
                    <span class="material-symbols-rounded crew-filter-icon"></span>
                </div>
                <div class="crew-filter-dropdown" id="crewFilterDropdown">
                    <div id="crewFilterContainer">
                        <!-- Checkboxes will be dynamically added here by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Calendar Container -->
        <div id="calendar-container" class="calendar-container">
            <div id="dp"></div>
        </div>        
    </div>

    <!-- jsPDF (latest stable version) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <!-- html2canvas (needed for screenshot) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script src="{{ url_for('static', filename='sidebar.js') }}"></script>
    <script src="{{ url_for('static', filename='calendar.js') }}"></script>
</body>
</html>




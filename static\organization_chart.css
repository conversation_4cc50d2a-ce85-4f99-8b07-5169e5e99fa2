/* Organization Chart Styles */

/* Header section with title and download button */
.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 20px;
}

.header-section h1 {
    margin: 0;
    color: #333;
    font-size: 2rem;
    font-weight: 600;
}

/* Download button styles */
.download-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 20px;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.download-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.download-btn .material-symbols-rounded {
    font-size: 18px;
}

.chart-container {
    margin: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    height: calc(104vh - 140px); /* Accounts for margins, padding, and header */
    overflow: hidden; /* Remove scroll */
}

.chart-controls {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Node avatar styles */
.node-avatar {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #E4E2E9;
}

.node-avatar-initials {
    background-color: #3498db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.node-avatar-image {
    background-color: #F9F9F9;
    object-fit: cover;
}



import requests
import os
import sqlite3
from datetime import datetime, timezone

# === CONFIG ===
MAXIMO_URL = "https://ems-lgensol.singlex.com/maximo/oslc/os/MXWO"
API_KEY    = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
DB_FILE    = "data/Data.db"
TABLE_NAME = "OJT_WorkOrder"

# === LOAD LAST REFRESH DATE ===
def load_last_refresh_date():
    filename = "last_refresh_date_OJT.txt"
    if os.path.exists(filename):
        with open(filename, "r") as f:
            return f.read().strip()

def save_last_refresh_date(iso_str):
    with open("last_refresh_date_OJT.txt", "w") as f:
        f.write(iso_str)

LAST_REFRESH_DATE = load_last_refresh_date()
print(f"LAST REFRESH DATE in UTC: {LAST_REFRESH_DATE}\n")

# === HEADERS ===
headers = {
    "apikey": API_KEY,
    "Accept": "application/json"
}

# === WHERE CLAUSE & PARAMS ===
where_clause = (
    'woclass="WORKORDER" and '
    'istask=0 and '
    'worktype!="DM" and '
    'zinfowoflag=0 and '
    'siteid="UTIL.GM" and '
    'ownergroup="GM.UT.U" and '
    f'changedate>"{LAST_REFRESH_DATE}"'
)

params = {
    "oslc.where":  where_clause,
    "oslc.select": "wonum,changedate,description,jpnum,assetnum,workorderid",
    "lean": "1"
}

# === FIRE THE REQUEST ===
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

resp = requests.get(
    MAXIMO_URL,
    headers=headers,
    params=params,
    verify=False
)

if resp.status_code == 200:
    data = resp.json()
    print("✅ Data retrieved successfully from Maximo")

    # Extract actual work order records
    records = data.get("member", [])
    
    # === SAVE TO SQLITE DATABASE ===
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()

    # Ensure the table exists before inserting
    cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            wonum TEXT,
            changedate TEXT,
            description TEXT,
            jpnum TEXT,
            assetnum TEXT,
            workorderid INTEGER
        )
    """)

    # Insert new rows (append)
    for row in records:
        cursor.execute(f"""
            INSERT INTO {TABLE_NAME} (wonum, changedate, description, jpnum, assetnum, workorderid)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            row.get("wonum"),
            row.get("changedate"),
            row.get("description"),
            row.get("jpnum"),
            row.get("assetnum"),
            row.get("workorderid")
        ))

    conn.commit()
    conn.close()
    print(f"📦 Appended data to SQLite table '{TABLE_NAME}' in {DB_FILE}")
    print(f"  {len(records)} records appended")

else:
    print(f"❌ Request failed: {resp.status_code}")
    print(resp.text)
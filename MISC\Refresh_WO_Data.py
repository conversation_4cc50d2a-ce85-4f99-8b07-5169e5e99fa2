import requests
import os
import sqlite3
import urllib3
import datetime

# Disable insecure HTTPS warnings (not recommended for production)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

print("🚀 Starting Maximo Work Order Sync Script...")

def load_last_refresh_date():
    filename = "last_refresh_date.txt"
    if os.path.exists(filename):
        with open(filename, "r") as f:
            return f.read().strip()

def save_last_refresh_date(iso_str):
    with open("last_refresh_date.txt", "w") as f:
        f.write(iso_str)

LAST_REFRESH_DATE = load_last_refresh_date()
print(f"LAST REFRESH DATE in UTC: {LAST_REFRESH_DATE}\n")

# =============== Constants ===================
BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "MXWO"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
DB_PATH = os.path.join("Data", "Data.db")
TABLE_NAME = "WorkOrder_Status"

print(f"🔧 Using DB path: {DB_PATH}")
print(f"📡 Connecting to Maximo at: {BASE_URL}")

# =============== Filter Configuration ===================

# initial filter where clause get ALL the data in EMS
# where_clause = (
#     'woclass="WORKORDER" and '
#     'istask=0 and '
#     'worktype!="DM" and '
#     'zinfowoflag=0 and '
#     'status in ["DRAFT","INPRG","OPCOMP","COMP","CLOSE"] and '
#     'siteid in ["UTIL.GM"] and '
#     'ownergroup in ["GM.UT.B","GM.UT.E","GM.UT.H","GM.UT.M","GM.UT.O"]'
# )

# # filter to where the work order changed after the most recent refresh
where_clause = (
    'woclass="WORKORDER" and '
    'istask=0 and '
    'worktype!="DM" and '
    'zinfowoflag=0 and '
    'siteid in ["UTIL.GM"] and '
    'ownergroup in ["GM.UT.B","GM.UT.E","GM.UT.H","GM.UT.M","GM.UT.O"] and '
    f'changedate>"{LAST_REFRESH_DATE}"'
)

params = {
    "oslc.where": where_clause,
    "oslc.select": "wonum,status,description,ownergroup,schedstart,schedfinish,actstart,actfinish,jpnum,assetnum,workorderid",
    "lean": "1"
}

headers = {
    "Accept": "application/json",
    "apikey": API_KEY
}

print("🔍 Sending request to Maximo with OSLC-safe filter...")

try:
    response = requests.get(
        f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}",
        headers=headers,
        params=params,
        verify=False
    )
    response.raise_for_status()
except requests.RequestException as e:
    print(f"❌ Request failed: {e}")
    exit(1)

print("✅ Request successful.")
data = response.json()
members = data.get("member", [])

print(f"📦 Received {len(members)} work order records from Maximo.")

# =============== Save to SQLite ===================
print("💾 Preparing to save records to local SQLite DB...")

os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# # Drop the existing table and recreate it with the new column
cursor.execute(f"DROP TABLE IF EXISTS {TABLE_NAME}")

print("📄 Creating table (if not exists)...")
cursor.execute(f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        wonum TEXT PRIMARY KEY,
        status TEXT,
        description TEXT,
        ownergroup TEXT,
        schedstart TEXT,
        schedfinish TEXT,
        actstart TEXT,
        actfinish TEXT,
        jpnum TEXT,
        assetnum TEXT,
        workorderid TEXT
    )
""")

print("📝 Inserting records...")
for i, row in enumerate(members, start=1):
    cursor.execute(f"""
        INSERT OR REPLACE INTO {TABLE_NAME} (
            wonum, status, description, ownergroup, schedstart, schedfinish, actstart, actfinish, jpnum, assetnum, workorderid
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        row.get("wonum"),
        row.get("status"),
        row.get("description"),
        row.get("ownergroup"),
        row.get("schedstart"),
        row.get("schedfinish"),
        row.get("actstart"),
        row.get("actfinish"),
        row.get("jpnum"),
        row.get("assetnum"),
        row.get("workorderid")
    ))
    if i % 100 == 0:
        print(f"  ➕ Inserted {i} records...")

# Remove rows with 'status="CAN"'
print("❌ Removing rows with status='CAN'...")
cursor.execute(f"DELETE FROM {TABLE_NAME} WHERE status='CAN'")

conn.commit()
conn.close()

# update last refresh date text file
new_refresh_date = datetime.datetime.now(datetime.timezone.utc).replace(microsecond=0).isoformat()
save_last_refresh_date(new_refresh_date)
print(f"🕒 Updated last refresh date to: {new_refresh_date}")

# ✅ All records saved to SQLite
print(f"✅ All records saved to SQLite: {DB_PATH} (table: {TABLE_NAME})")

# 🔢 Count rows in the table
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()
cursor.execute(f"SELECT COUNT(*) FROM {TABLE_NAME}")
row_count = cursor.fetchone()[0]
conn.close()

print(f"📊 Total rows in {TABLE_NAME}: {row_count}")
print("🏁 Script finished successfully.")

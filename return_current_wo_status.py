import sqlite3
import pandas as pd

def refresh_workorder_status():
    # Database path
    DB_PATH = "data/DataNew.db"

    # Connect to SQLite
    conn = sqlite3.connect(DB_PATH)

    # Step 1: Load OJT_WO_status into a DataFrame
    query = "SELECT * FROM OJT_WO_status;"
    df = pd.read_sql_query(query, conn)

    # Step 2: Ensure changedate is a datetime type
    df['changedate'] = pd.to_datetime(df['changedate'], errors='coerce')

    # Step 3: Sort by changedate (descending)
    df_sorted = df.sort_values(by='changedate', ascending=False)

    # Step 4: Drop duplicates, keeping latest record per 'parent'
    df_latest = df_sorted.drop_duplicates(subset=['parent'], keep='first')

    # Step 5: Save results into OJT_WorkOrder
    df_latest.to_sql('OJT_WorkOrder', conn, if_exists='replace', index=False)

    # Close the connection
    conn.close()

    print("✅ Latest parent records saved to 'OJT_WorkOrder' successfully.")

# Allow standalone execution
if __name__ == '__main__':
    refresh_workorder_status()
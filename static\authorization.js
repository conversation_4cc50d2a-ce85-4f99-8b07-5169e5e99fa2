// Function to sort the table by Access level and then by Employee Login
function sortTable() {
    const tbody = document.querySelector('#employeeAccessTable tbody');
    if (!tbody) return; // Exit if table doesn't exist yet

    const rows = Array.from(tbody.getElementsByTagName('tr'));
    if (rows.length === 0) return; // Exit if no rows

    // Define access level priority (Admin > User > View)
    function getAccessPriority(accessLevel) {
        switch(accessLevel) {
            case 'Admin': return 1;
            case 'User': return 2;
            case 'View': return 3;
            default: return 4; // Any other value comes last
        }
    }

    // Sort the rows
    rows.sort((a, b) => {
        // Get access level cells
        const accessA = a.getElementsByTagName('td')[2].textContent;
        const accessB = b.getElementsByTagName('td')[2].textContent;

        // Get employee login cells
        const loginA = a.getElementsByTagName('td')[1].textContent.toLowerCase();
        const loginB = b.getElementsByTagName('td')[1].textContent.toLowerCase();

        // Compare access levels first
        const accessPriorityA = getAccessPriority(accessA);
        const accessPriorityB = getAccessPriority(accessB);

        if (accessPriorityA !== accessPriorityB) {
            return accessPriorityA - accessPriorityB;
        }

        // If access levels are the same, sort by employee login alphabetically
        return loginA.localeCompare(loginB);
    });

    // Remove all existing rows
    while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
    }

    // Add sorted rows back to the table
    rows.forEach(row => {
        tbody.appendChild(row);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    const addAccessBtn = document.getElementById('addAccessBtn');
    const departmentInput = document.getElementById('department');
    const employeeLoginInput = document.getElementById('employeeLogin');
    const accessInput = document.getElementById('access');
    const employeeAccessTable = document.getElementById('employeeAccessTable');

    // Sort the table on page load
    sortTable();

    // Add event listener for add button click
    addAccessBtn.addEventListener('click', function() {
        // Get input values
        const department = departmentInput.value.trim();
        const employeeLogin = employeeLoginInput.value.trim();
        const access = accessInput.value.trim();

        // Validate inputs
        if (department === '' || employeeLogin === '' || access === '') {
            alert('Please fill in all fields');
            return;
        }

        // Create authorization data object
        const authData = {
            department: department,
            employee_login: employeeLogin,
            access_level: access
        };

        // Send data to the server
        fetch('/api/authorizations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(authData),
        })
        .then(response => {
            if (response.status === 409) {
                alert('This authorization already exists!');
                throw new Error('Authorization already exists');
            }
            return response.json();
        })
        .then(data => {
            // Add the new authorization to the table
            addAuthorizationRow(data);
            // Clear the inputs
            departmentInput.value = '';
            employeeLoginInput.value = '';
            accessInput.value = '';
            // Sort the table after adding a new row
            sortTable();
        })
        .catch(error => {
            console.error('Error:', error);
        });
    });

    function addAuthorizationRow(auth) {
        const newRow = employeeAccessTable.insertRow(-1);
        newRow.setAttribute('data-id', auth.id);

        const departmentCell = newRow.insertCell(0);
        const loginCell = newRow.insertCell(1);
        const accessCell = newRow.insertCell(2);
        const actionsCell = newRow.insertCell(3);

        departmentCell.textContent = auth.department;
        loginCell.textContent = auth.employee_login;
        accessCell.textContent = auth.access_level;
        actionsCell.innerHTML = `<button class="btn-delete" onclick="deleteAuthorization('${auth.id}')">Delete</button>`;
    }

    // Add filter functionality
    document.querySelectorAll('.table-filter').forEach(input => {
        input.addEventListener('input', filterTable);
    });

    function filterTable() {
        const departmentValue = document.getElementById('departmentFilter').value.toLowerCase();
        const loginValue = document.getElementById('employeeLoginFilter').value.toLowerCase();
        const accessValue = document.getElementById('accessFilter').value.toLowerCase();

        // Get all rows in the table body
        const rows = document.querySelector('#employeeAccessTable tbody').getElementsByTagName('tr');

        // Loop through all table rows
        for (let i = 0; i < rows.length; i++) {
            const departmentCell = rows[i].getElementsByTagName('td')[0];
            const loginCell = rows[i].getElementsByTagName('td')[1];
            const accessCell = rows[i].getElementsByTagName('td')[2];

            if (departmentCell && loginCell && accessCell) {
                const departmentText = departmentCell.textContent.toLowerCase();
                const loginText = loginCell.textContent.toLowerCase();
                const accessText = accessCell.textContent.toLowerCase();

                // Check if the row should be displayed based on all filters
                const departmentMatch = departmentText.includes(departmentValue);
                const loginMatch = loginText.includes(loginValue);
                const accessMatch = accessText.includes(accessValue);

                if (departmentMatch && loginMatch && accessMatch) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }
    }
});

function deleteAuthorization(id) {
    // Get the row to be deleted
    const rowToDelete = document.querySelector(`tr[data-id="${id}"]`);

    // Check if this is an Admin user
    const accessLevel = rowToDelete.getElementsByTagName('td')[2].textContent;

    if (accessLevel === 'Admin') {
        // Count the total number of Admin users
        const adminCount = countAdminUsers();

        // If this is the last Admin, show warning but let server handle validation
        if (adminCount <= 1) {
            if (!confirm('Warning: You are about to delete the last Admin user. This action will be blocked by the server unless another Admin exists. Continue?')) {
                return;
            }
        }
    }

    // Proceed with deletion if not the last Admin
    if (confirm('Are you sure you want to delete this authorization?')) {
        fetch(`/api/authorizations/${id}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (response.ok) {
                rowToDelete.remove();
                // Sort the table after deletion
                sortTable();
            } else {
                // Handle server error responses
                return response.json().then(errorData => {
                    alert(errorData.error || 'Failed to delete authorization');
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the authorization');
        });
    }
}

// Function to count the number of Admin users in the table
function countAdminUsers() {
    const rows = document.querySelector('#employeeAccessTable tbody').getElementsByTagName('tr');
    let adminCount = 0;

    for (let i = 0; i < rows.length; i++) {
        const accessCell = rows[i].getElementsByTagName('td')[2];
        if (accessCell && accessCell.textContent === 'Admin') {
            adminCount++;
        }
    }

    return adminCount;
}

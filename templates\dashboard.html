<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Ultium Cells</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='sidebar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0">
    
    <style>
        .dashboard-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .welcome-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .welcome-section h1 {
            color: #151A2D;
            margin-bottom: 10px;
        }
        
        .welcome-section p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 2rem;
        }
        
        .stat-card p {
            margin: 0;
            opacity: 0.9;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            text-decoration: none;
            color: #495057;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .action-btn .material-symbols-rounded {
            margin-right: 10px;
            font-size: 1.5rem;
        }

        .loading {
            opacity: 0.6;
            animation: pulse 1.5s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            from { opacity: 0.6; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    {% include 'sidebar.html' %}
    
    <div class="container">
        <div class="dashboard-content">
            <div class="welcome-section">
                <h1>Welcome to Ultium Cells Dashboard</h1>
                <p>Manage your facility operations, track work orders, and monitor performance.</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="total-workorders">-</h3>
                    <p>Total Work Orders</p>
                </div>
                <div class="stat-card">
                    <h3 id="completed-workorders">-</h3>
                    <p>Completed Work Orders</p>
                </div>
                <div class="stat-card">
                    <h3 id="in-progress-workorders">-</h3>
                    <p>In Progress</p>
                </div>
                <div class="stat-card">
                    <h3 id="unique-crews">-</h3>
                    <p>Active Crews</p>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='sidebar.js') }}"></script>

    <script>
        // Function to load work order summary
        function loadWorkOrderSummary() {
            // Add loading state
            const statCards = document.querySelectorAll('.stat-card h3');
            statCards.forEach(card => {
                card.textContent = '...';
                card.parentElement.classList.add('loading');
            });

            fetch('/api/current-workorders')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const records = data.records;

                        // Calculate statistics
                        const totalWorkOrders = records.length;
                        const completedWorkOrders = records.filter(record =>
                            record.status && record.status.toLowerCase().includes('comp')
                        ).length;
                        const inProgressWorkOrders = records.filter(record =>
                            record.status && (
                                record.status.toLowerCase().includes('inprg') ||
                                record.status.toLowerCase().includes('progress') ||
                                record.status.toLowerCase().includes('active')
                            )
                        ).length;

                        // Get unique crews
                        const uniqueCrews = new Set(
                            records.map(record => record.crew).filter(crew => crew)
                        ).size;

                        // Update the dashboard cards
                        document.getElementById('total-workorders').textContent = totalWorkOrders;
                        document.getElementById('completed-workorders').textContent = completedWorkOrders;
                        document.getElementById('in-progress-workorders').textContent = inProgressWorkOrders;
                        document.getElementById('unique-crews').textContent = uniqueCrews;

                        // Remove loading state
                        document.querySelectorAll('.stat-card').forEach(card => {
                            card.classList.remove('loading');
                        });
                    } else {
                        console.error('Error loading work orders:', data.message);
                        // Set error indicators
                        document.getElementById('total-workorders').textContent = 'Error';
                        document.getElementById('completed-workorders').textContent = 'Error';
                        document.getElementById('in-progress-workorders').textContent = 'Error';
                        document.getElementById('unique-crews').textContent = 'Error';

                        // Remove loading state
                        document.querySelectorAll('.stat-card').forEach(card => {
                            card.classList.remove('loading');
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching work orders:', error);
                    // Set error indicators
                    document.getElementById('total-workorders').textContent = 'Error';
                    document.getElementById('completed-workorders').textContent = 'Error';
                    document.getElementById('in-progress-workorders').textContent = 'Error';
                    document.getElementById('unique-crews').textContent = 'Error';

                    // Remove loading state
                    document.querySelectorAll('.stat-card').forEach(card => {
                        card.classList.remove('loading');
                    });
                });
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadWorkOrderSummary();
        });
    </script>
</body>
</html>

document.addEventListener('DOMContentLoaded', function() {
    let dp = null;
    let allEvents = []; // Store all events for filtering
    let selectedCrews = new Set(['A', 'B', 'C', 'D']); // All crews selected by default

    // Function to generate colors based on crew
    function getCrewColor(crew) {
        const colors = {
            'A': '#6b007b',    // Red
            'B': '#118dff',    // Teal
            'C': '#e66c37',    // Blue
            'D': '#12239e',    // Green
        };

        // Handle empty or null crew values
        if (!crew || crew === '') {
            return '#CCCCCC';  // Gray for unassigned
        }

        // If crew not in predefined colors, generate a hash-based color
        if (!colors[crew]) {
            let hash = 0;
            for (let i = 0; i < crew.length; i++) {
                hash = crew.charCodeAt(i) + ((hash << 5) - hash);
            }
            const hue = Math.abs(hash) % 360;
            return `hsl(${hue}, 70%, 70%)`;
        }

        return colors[crew];
    }

    // Function to filter events based on selected crews
    function filterEventsByCrew() {
        const filteredEvents = allEvents.filter(event => {
            // Show events with no crew if no specific crew is selected, or if the event's crew is selected
            if (!event.crew || event.crew === '') {
                return selectedCrews.size > 0; // Show unassigned if any crew is selected
            }
            return selectedCrews.has(event.crew);
        });
        
        dp.events.list = filteredEvents;
        dp.update();
    }

    // Function to handle crew filter changes
    function handleCrewFilterChange() {
        selectedCrews.clear();
        
        // Get all checked crew checkboxes
        const checkedBoxes = document.querySelectorAll('.crew-checkbox:checked');
        checkedBoxes.forEach(checkbox => {
            selectedCrews.add(checkbox.value);
        });
        
        // Update the calendar display
        filterEventsByCrew();
    }

    // Function to update crew filter summary
    function updateCrewFilterSummary() {
        const summary = document.getElementById('crewFilterSummary');
    }

    // Function to initialize crew filter
    function initializeCrewFilter() {
        const crews = ['A', 'B', 'C', 'D'];
        const crewFilterContainer = document.getElementById('crewFilterContainer');
        
        crews.forEach(crew => {
            const label = document.createElement('label');
            label.className = 'crew-filter-item';
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'crew-checkbox';
            checkbox.value = crew;
            checkbox.checked = true; // All crews checked by default
            checkbox.addEventListener('change', handleCrewFilterChange);
            
            const span = document.createElement('span');
            span.textContent = ` ${crew}-Crew  `;
            span.className = 'crew-label';
            span.style.color = getCrewColor(crew);
            
            label.appendChild(checkbox);
            label.appendChild(span);
            crewFilterContainer.appendChild(label);
        });
        
        // Initialize summary
        updateCrewFilterSummary();
    }

    function initializeCalendar() {
        if (dp) {
            dp.dispose();
        }
    
        $('#dp').empty();
        dp = new DayPilot.Month("dp");
        dp.viewType = "Weeks";
        dp.weeks = 6;
        dp.cssClassPrefix = 'calendar_white';
        dp.headerDateFormat = "MMMM yyyy";
        dp.showWeekend = true;
        dp.weekStarts = 0;
        dp.onEventClick = handleEventClick;
        dp.onTimeRangeSelected = handleTimeRangeSelected;
        dp.timeRangeSelectedHandling = "Enabled";
        dp.eventHeight = 100;
        dp.cellHeight = 100;
        dp.eventWrap = true;

        dp.onBeforeEventRender = function(args) {
            const color = args.data.borderColor || '#000000';
        
            args.data.backColor = 'transparent';
            args.data.borderColor = color;
            args.data.barHidden = true;
            args.data.cssClass = 'outline-event';
        
            // Force text color and preserve line breaks
            args.data.html = `<div style="color:black; white-space:pre-wrap;">${args.data.text}</div>`;
        };

        loadEvents();
        dp.init();  // Initialize once here, after setup and event loading
    }
    

    function handleEventClick(args) {
        // Handle event click
        const modal = new DayPilot.Modal({
            onClosed: function(args) {
                if (args.result) {
                    dp.events.update(args.result);
                }
            }
        });
    }

    function handleTimeRangeSelected(args) {
        // Handle time range selection
        const modal = new DayPilot.Modal({
            onClosed: function(args) {
                dp.clearSelection();
                if (args.result) {
                    dp.events.add(args.result);
                }
            }
        });
    }

    // Load events function
    function loadEvents() {
        // Load events from the WorkOrder_Draft table
        fetch('/api/events')
            .then(response => response.json())
            .then(data => {
                // console.log('Raw API data:', data); // Debug log

                const workOrderEvents = data.map(wo => {
                    // Map the correct field names from the API response
                    // The API returns crew-based coloring from the server
                    const event = {
                        id: wo.id,
                        text: "‣ " + (wo.id || '‣ No WO#') +"\n‣ " + (wo.equipment || '\n‣ No Equipment') + "\n‣ " + (wo.title || '\n‣ No Description'),
                        start: new DayPilot.Date(wo.start),
                        end: new DayPilot.Date(wo.end),
                        ownergroup: wo.ownergroup,
                        jpnum: wo.jpnum,
                        crew: wo.crew,
                        borderColor: wo.color || getCrewColor(wo.crew || ''),
                        backColor: 'transparent',
                        barHidden: true,
                        cssClass: 'outline-event'
                    };

                    // console.log('Mapped event:', event); // Debug log
                    return event;
                });

                // Store all events for filtering
                allEvents = workOrderEvents;
                
                // Apply crew filter
                filterEventsByCrew();
                
                // console.log('Total events loaded:', workOrderEvents.length); // Debug log
            })
            .catch(error => {
                console.error('Error loading work orders:', error);
                allEvents = [];
                dp.events.list = [];
                dp.update();
            });
    }

    // Navigation functionality
    function updateDatePicker() {
        const datePicker = document.getElementById('datePicker');
        if (dp && datePicker) {
            const currentDate = dp.startDate || DayPilot.Date.today();
            datePicker.value = currentDate.toString('yyyy-MM-dd');
        }
    }

    // Previous button
    document.getElementById('prevBtn').addEventListener('click', function() {
        if (dp) {
            dp.startDate = dp.startDate.addMonths(-1);
            dp.update();
            updateDatePicker();
        }
    });

    // Next button
    document.getElementById('nextBtn').addEventListener('click', function() {
        if (dp) {
            dp.startDate = dp.startDate.addMonths(1);
            dp.update();
            updateDatePicker();
        }
    });

    // Today button
    document.getElementById('todayBtn').addEventListener('click', function() {
        if (dp) {
            dp.startDate = DayPilot.Date.today();
            dp.update();
            updateDatePicker();
        }
    });

    // Date picker change
    document.getElementById('datePicker').addEventListener('change', function() {
        if (dp && this.value) {
            dp.startDate = new DayPilot.Date(this.value);
            dp.update();
        }
    });
    
    function exportToPDF() {
        const exportBtn = document.querySelector('.export-btn');
        const originalText = exportBtn.innerHTML;
    
        exportBtn.disabled = true;
        exportBtn.innerHTML = '<span class="material-symbols-rounded">hourglass_empty</span>Exporting...';
    
        try {
            const now = new Date();
            const dateStr = now.toISOString().split('T')[0];
            const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
            const filename = `TODO_WorkOrders_${dateStr}_${timeStr}.pdf`;
    
            const ganttContainer = document.getElementById('dp');
            if (!ganttContainer) throw new Error("Gantt container 'dp' not found.");
    
            const options = {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: ganttContainer.scrollWidth,
                height: ganttContainer.scrollHeight,
                scrollX: 0,
                scrollY: 0
            };
    
            html2canvas(ganttContainer, options).then(canvas => {
                try {
                    const jsPDFLib = window.jspdf?.jsPDF || window.jsPDF;
    
                    const pxToMM = px => px * 0.264583;
                    const imgWidthPx = canvas.width;
                    const imgHeightPx = canvas.height;
                    let imgWidthMM = pxToMM(imgWidthPx);
                    let imgHeightMM = pxToMM(imgHeightPx);
    
                    // Margins in mm
                    const margin = 20;
                    const maxPDFDimension = 14400 * 0.352778; // jsPDF max in mm ≈ 5080
    
                    // Include margin in size check
                    const totalWidth = imgWidthMM + 2 * margin;
                    const totalHeight = imgHeightMM + 2 * margin;
    
                    // Downscale if over jsPDF limit
                    const scale = Math.min(1, maxPDFDimension / Math.max(totalWidth, totalHeight));
                    imgWidthMM *= scale;
                    imgHeightMM *= scale;
    
                    const pageWidth = imgWidthMM + 2 * margin;
                    const pageHeight = imgHeightMM + 2 * margin;
    
                    const orientation = pageWidth > pageHeight ? 'landscape' : 'portrait';
    
                    const pdf = new jsPDFLib({
                        orientation,
                        unit: 'mm',
                        format: [pageWidth, pageHeight],
                        compress: false
                    });
    
                    const imgData = canvas.toDataURL('image/png', 1.0);
                    pdf.addImage(imgData, 'PNG', margin, margin, imgWidthMM, imgHeightMM, '', 'FAST');
                    pdf.save(filename);
    
                    console.log('PDF exported successfully');
                } catch (pdfError) {
                    console.error('Error creating PDF:', pdfError);
                    alert('Error creating PDF. Please try again.');
                }
    
                exportBtn.disabled = false;
                exportBtn.innerHTML = originalText;
            }).catch(canvasError => {
                console.error('Error capturing canvas:', canvasError);
                alert('Error capturing chart. Please try again.');
                exportBtn.disabled = false;
                exportBtn.innerHTML = originalText;
            });
    
        } catch (error) {
            console.error('Error during export:', error);
            alert('Error during export. Please try again.');
            exportBtn.disabled = false;
            exportBtn.innerHTML = originalText;
        }
    }
    
    window.exportToPDF = exportToPDF;
    
    // Initialize crew filter
    initializeCrewFilter();

    // Initialize with month view (only view available now)
    initializeCalendar();

    // Set initial date picker value
    setTimeout(updateDatePicker, 10000000000000);
});
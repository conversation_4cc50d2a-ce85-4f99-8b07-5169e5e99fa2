import sqlite3
import os
import pandas as pd

def export_table(table_name='OrgChart', export_excel=True):
    # Define database path
    db_path = os.path.join('Data', 'DataNew.db')
    print(f"Database path: {os.path.abspath(db_path)}")

    # Check if the database file exists
    if not os.path.exists(db_path):
        print(f"ERROR: Database file does not exist: {db_path}")
        return

    # Connect to the SQLite database
    conn = sqlite3.connect(db_path)

    try:
        # Read the table into a DataFrame
        query = f"SELECT * FROM {table_name}"
        df = pd.read_sql_query(query, conn)

        # Export to Excel if requested
        if export_excel:
            export_path = f"{table_name}_export.xlsx"
            df.to_excel(export_path, index=False)
            print(f"SUCCESS: Exported data to {export_path}")

    except Exception as e:
        print(f"ERROR: {e}")
    finally:
        conn.close()

if __name__ == '__main__':
    export_table()

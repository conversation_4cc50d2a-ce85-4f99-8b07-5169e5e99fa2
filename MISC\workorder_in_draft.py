import requests
import urllib3
import os
import sqlite3
from datetime import datetime, timedelta
import pandas as pd

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# =============== Get work order in DRAFT data ===================
# Constants
BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "MXWO"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
DB_PATH = os.path.join("Data", "Data.db")
TABLE_NAME = "WorkOrder_Draft"

# OSLC-safe WHERE clause
where_clause = (
    'status="DRAFT" and '
    'woclass in ["WORKORDER","ACTIVITY"] and '
    'historyflag=0 and '
    'istask=0 and '
    'siteid="UTIL.GM" and '
    'ownergroup="GM.UT.H"'
    # 'assetnum="%DHU%"'
)

# # OSLC-safe WHERE clause
# where_clause = (
#     'status="DRAFT" and '
#     'woclass in ["WORKORDER","ACTIVITY"] and '
#     'historyflag=0 and '
#     'istask=0 and '
#     'siteid="UTIL.GM" and '
#     'ownergroup in ["GM.UT.B","GM.UT.E","GM.UT.M","GM.UT.O","GM.UT.H"]'
# )

params = {
    "oslc.where": where_clause,
    "oslc.select": "wonum,description,ownergroup,schedstart,schedfinish,jpnum,assetnum",
    "lean": "1"
}

headers = {
    "Accept": "application/json",
    "apikey": API_KEY
}

print("🔍 Querying work orders with OSLC-safe filter...")
response = requests.get(f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}", headers=headers, params=params, verify=False)

if response.status_code == 200:
    data = response.json()
    members = data.get("member", [])
    # filtered_members = [wo for wo in members if 'july outage' in (wo.get('description') or '').lower()]
    # members = filtered_members
    print(f"✅ Retrieved {len(members)} records.")

    # Ensure Data folder exists
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

    # Save to SQLite
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # # Drop the existing table and recreate it with the new column
    # cursor.execute(f"DROP TABLE IF EXISTS {TABLE_NAME}")

    # Create table with all needed columns
    cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            wonum TEXT PRIMARY KEY,
            description TEXT,
            ownergroup TEXT,
            schedstart TEXT,
            schedfinish TEXT,
            jpnum TEXT,
            assetnum TEXT
        )
    """)
    cursor.execute(f"DELETE FROM {TABLE_NAME}")

    for row in members:
        cursor.execute(f"""
            INSERT OR REPLACE INTO {TABLE_NAME} (
                wonum, description, ownergroup, schedstart, schedfinish, jpnum, assetnum
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            row.get("wonum"),
            row.get("description"),
            row.get("ownergroup"),
            row.get("schedstart"),
            row.get("schedfinish"),
            row.get("jpnum"),
            row.get("assetnum")
        ))

    conn.commit()
    conn.close()
    print(f"💾 Saved to SQLite: {DB_PATH} (table: {TABLE_NAME})")

else:
    print(f"❌ Request failed: {response.status_code}")
    print(response.text)

# =============== Assigned work orders to crew ===============
'''
Adding 8 more columns and creating a new table call "TODO_WorkOrder"
Actual_Plan_Start_Date: Adjusted schedstart based on the schedule.
Actual_Plan_End_Date: Actual_Plan_Start_Date + duration
Shift_Start_Datetime: round down Actual_Plan_Start_Date to nearest shift start datetime
Shift_End_Datetime: round up Actual_Plan_End_Date to nearest shift end datetime
Crew: from get_crew_letter()
CUSUM_hours: CUSOM of current shift work order hours
Groupby: definition of how the work orders are grouped together/scheduled.
Equipment: Inner Join with equipment_master table assetnum

Save "TODO_WorkOrder" table to Data.db
'''
WORK_ORDER_HOUR_PER_SHIFT = 18  # Maximum work hours per shift

# Mapping dictionary for 'ownergroup' codes
department_mapping = {
    'GM.UT.B': 'Boiler',
    'GM.UT.H': 'HVAC',
    'GM.UT.M': 'Mechanical',
    'GM.UT.O': 'Operator',
    'GM.UT.E': 'Electrical'
}

def get_crew_letter(shift_start) -> str:
    # Handle NaT values
    if pd.isna(shift_start):
        return ""

    # Convert to timezone-naive datetime if needed
    if hasattr(shift_start, 'tz') and shift_start.tz is not None:
        shift_start = shift_start.tz_localize(None)

    # Ensure it's a datetime object
    if not isinstance(shift_start, datetime):
        shift_start = pd.to_datetime(shift_start).to_pydatetime()

    # Reference date (January 1, 1900)
    ref_date = datetime(1900, 1, 1)

    # Calculate days since reference date
    days_since_ref_date_shift = (shift_start - ref_date).days
    days_since_ref_date_2024 = (datetime(2024, 1, 1) - ref_date).days

    # Shift pattern determination
    if 6 <= shift_start.hour < 18:
        # Day shifts (A or B)
        mod_value = (days_since_ref_date_shift - days_since_ref_date_2024 + 45292 + 18) % 6
        if mod_value < 3:
            return "A"
        else:
            return "C"
    else:
        # Night shifts (C or D)
        mod_value = (days_since_ref_date_shift - days_since_ref_date_2024 + 45292 - 12) % 6
        if mod_value < 3:
            return "B"
        else:
            return "D"

def get_shift_start_end(shift_start_time):
    """Determine shift start and end datetime based on given start time."""
    if pd.isna(shift_start_time):  # Ensure we handle missing values
        return pd.NaT, pd.NaT

    # Convert to timezone-naive datetime if needed
    if hasattr(shift_start_time, 'tz') and shift_start_time.tz is not None:
        shift_start_time = shift_start_time.tz_localize(None)

    # Convert to datetime (if not already)
    shift_start_time = pd.to_datetime(shift_start_time)

    if 6 <= shift_start_time.hour < 18:
        return shift_start_time.replace(hour=6, minute=0, second=0, microsecond=0), shift_start_time.replace(hour=18, minute=0, second=0, microsecond=0)
    else:
        if shift_start_time.hour < 6:
            shift_start = shift_start_time.replace(hour=18, minute=0, second=0, microsecond=0) - timedelta(days=1)
        else:
            shift_start = shift_start_time.replace(hour=18, minute=0, second=0, microsecond=0)
        return shift_start, shift_start + timedelta(hours=12)

# =============== Assign PM Activity ===================
'''
If the jpnum equal a value on the PM Activity list, the schedstart and end (jpnum in Workorder_Draft table)
'''
try:
    pm_activity_df = pd.read_excel('data/PM Activity.xlsx')
except FileNotFoundError:
    print("⚠️ PM Activity.xlsx not found, creating empty DataFrame")
    pm_activity_df = pd.DataFrame(columns=['Activity No', 'Plan End Date'])

# Reconnect to database
conn = sqlite3.connect(DB_PATH)
df = pd.read_sql_query(f"SELECT * FROM {TABLE_NAME}", conn)
conn.close()
# Apply the department mapping
df['ownergroup'] = df['ownergroup'].map(department_mapping)

# Convert datetime columns and remove timezone info to avoid compatibility issues
df['schedstart'] = pd.to_datetime(df['schedstart'], errors='coerce', utc=True).dt.tz_localize(None)
df['schedfinish'] = pd.to_datetime(df['schedfinish'], errors='coerce', utc=True).dt.tz_localize(None)

# Duration is Sched Finish - Sched Start in minutes
df['Duration (Min)'] = (df['schedfinish'] - df['schedstart']).dt.total_seconds() / 60

# Replace zero durations with 60 minutes
df['Duration (Min)'] = df['Duration (Min)'].replace(0, 60)
df['Duration (Min)'] = df['Duration (Min)'].fillna(60)  # Fill NaN with 60 minutes

# Initialize new columns with proper dtypes
df['Actual Plan Start Date'] = pd.to_datetime(pd.NaT)
df['Actual Plan End Date'] = pd.to_datetime(pd.NaT)
df['Shift Start DateTime'] = pd.to_datetime(pd.NaT)
df['Shift End DateTime'] = pd.to_datetime(pd.NaT)
df['Crew'] = ''
df['CUSUM Hours'] = 0.0
df['Groupby'] = ''

# Sort the data by 'schedstart'
df.sort_values(by='schedstart', inplace=True)

###############################----PM ACTIVITY---###############################
# Identify matching activities
if not pm_activity_df.empty and 'Activity No' in pm_activity_df.columns:
    matching_activities = df['jpnum'].isin(pm_activity_df['Activity No'].values)

    # Assign 'Actual Plan Start Date' and 'Actual Plan End Date' for matching activities
    df.loc[matching_activities, 'Actual Plan Start Date'] = pd.to_datetime(df.loc[matching_activities, 'schedstart'])
    if 'Plan End Date' in pm_activity_df.columns:
        # Merge to get Plan End Date
        pm_dict = dict(zip(pm_activity_df['Activity No'], pd.to_datetime(pm_activity_df['Plan End Date'])))
        df.loc[matching_activities, 'Actual Plan End Date'] = df.loc[matching_activities, 'jpnum'].map(pm_dict)
    else:
        df.loc[matching_activities, 'Actual Plan End Date'] = pd.to_datetime(df.loc[matching_activities, 'schedfinish'])
else:
    matching_activities = pd.Series([False] * len(df), index=df.index)

# Convert 'Actual Plan Start Date' and 'Actual Plan End Date' to datetime
df['Actual Plan Start Date'] = pd.to_datetime(df['Actual Plan Start Date'], errors='coerce')
df['Actual Plan End Date'] = pd.to_datetime(df['Actual Plan End Date'], errors='coerce')

# Apply function to get shift start and end times
df[['Shift Start DateTime', 'Shift End DateTime']] = df['Actual Plan Start Date'].apply(
    lambda x: pd.Series(get_shift_start_end(x))
)

# Convert shift times to datetime
df['Shift Start DateTime'] = pd.to_datetime(df['Shift Start DateTime'], errors='coerce')
df['Shift End DateTime'] = pd.to_datetime(df['Shift End DateTime'], errors='coerce')

# Assign 'Crew' column based on shift start times
df.loc[matching_activities, 'Crew'] = df.loc[matching_activities, 'Shift Start DateTime'].apply(get_crew_letter)

# Compute 'CUSUM Hours' for matching activities
df.loc[matching_activities, 'CUSUM Hours'] = (
    df.loc[matching_activities, 'Actual Plan End Date'] - df.loc[matching_activities, 'Actual Plan Start Date']
).dt.total_seconds() / 3600  # Convert to hours

# Fill NaN values in 'CUSUM Hours' with 0
df['CUSUM Hours'] = df['CUSUM Hours'].fillna(0)

# Compute cumulative sum of hours within each shift and department
df['CUSUM Hours'] = df.groupby(['Shift Start DateTime', 'ownergroup'])['CUSUM Hours'].cumsum()

# Assign 'Groupby' column for matching activities
df.loc[matching_activities, 'Groupby'] = 'PM ACTIVITY'

###############################----WORK ORDERS NOT DUE---###############################
# if work order schedstart is greater than today, set Actual Plan Start Date to the same as schedstart

###############################----BCU-100---###############################
# Add Equipment Description column if it doesn't exist
if 'Equipment Description' not in df.columns:
    df['Equipment Description'] = df['description']  # Use description as fallback

# Filter rows where 'Equipment Description' contains 'BCU-100'
bcu_100_mask = (
    df['Equipment Description'].str.contains("BCU-100", na=False, case=False) &
    (df['Groupby'].isna() | (df['Groupby'] == ''))
)
print(f"BCU-100 work orders found: {len(df[bcu_100_mask])}")

# Get the current date-time and calculate the initial plan start
current_datetime = datetime.now()
if current_datetime.hour < 6:
    initial_plan_start =  (current_datetime - timedelta(days=1)).replace(hour=18, minute=0, second=0, microsecond=0)
else:
    initial_plan_start = current_datetime.replace(hour=18, minute=0, second=0, microsecond=0)

# Iterate over the filtered rows
for index in df[bcu_100_mask].index:
    # Get max CUSUM hours for the shift
    cusum_max = df.loc[(df['Shift Start DateTime'] == initial_plan_start) & 
                        (df['ownergroup'] == "HVAC"), 'CUSUM Hours'].max()
    cusum_max = cusum_max if pd.notna(cusum_max) else 0  # Handle NaN case

    # If CUSUM exceeds shift limit, move to next shift
    while cusum_max > WORK_ORDER_HOUR_PER_SHIFT:
        initial_plan_start += timedelta(days=1)
        cusum_max = df.loc[(df['Shift Start DateTime'] == initial_plan_start) & 
                           (df['ownergroup'] == "HVAC"), 'CUSUM Hours'].max()
        cusum_max = cusum_max if pd.notna(cusum_max) else 0  # Reset for the new shift
        print("=================================================")
        print(f"New Shift Start: {initial_plan_start}, Updated CUSUM: {cusum_max}")
    

    df.loc[index, 'Actual Plan Start Date'] = initial_plan_start
    df.at[index, 'Actual Plan End Date'] = initial_plan_start + timedelta(hours=df['Duration (Min)'][index] / 60)
    
    shift_start, shift_end = get_shift_start_end(df.at[index, 'Actual Plan End Date'])
    df.at[index, 'Shift Start DateTime'] = pd.to_datetime(shift_start)
    df.at[index, 'Shift End DateTime'] = pd.to_datetime(shift_end)
    df.at[index, 'Crew'] = get_crew_letter(shift_start)
    df.at[index, 'CUSUM Hours'] = cusum_max + (df['Duration (Min)'][index] / 60)
    df.at[index, 'Groupby'] = 'BCU-100'

    print(f"{index} | {df.at[index, 'wonum']} | {df.at[index, 'Equipment Description']} | "
          f"{df.at[index, 'Shift Start DateTime']} | {df.at[index, 'Duration (Min)']} | "
          f"{df.at[index, 'Crew']} | {cusum_max} | {df.at[index, 'CUSUM Hours']}")
###############################----ALL THE REST OF THE WORK ORDERS---###############################
# Filter rows where [Groupby] is NULL
rest_mask = (df['Groupby'].isna() | (df['Groupby'] == ''))
print(f"Remaining work orders to process: {len(df[rest_mask])}")

# Initialize the actual_plan_start depending on current date and time
if current_datetime.hour < 6:
    initial_plan_start =  (current_datetime - timedelta(days=1)).replace(hour=18, minute=0, second=0, microsecond=0)
elif current_datetime.hour < 18:
    initial_plan_start = current_datetime.replace(hour=6, minute=0, second=0, microsecond=0)
else:
    initial_plan_start = current_datetime.replace(hour=18, minute=0, second=0, microsecond=0)


# Iterate over each unique ownergroup
for department in df['ownergroup'].dropna().unique():
    # Filter rows for the current department
    department_mask = (df['ownergroup'] == department) & rest_mask

    # Iterate over the filtered rows for this department
    for index in df[department_mask].index:
        # Get max CUSUM hours for the shift within the current department
        cusum_max = df.loc[
            (df['Shift Start DateTime'] == initial_plan_start) & (df['ownergroup'] == department),
            'CUSUM Hours'
        ].max()

        cusum_max = cusum_max if pd.notna(cusum_max) else 0  # Handle NaN case

        # If CUSUM exceeds shift limit, move to next shift
        while cusum_max > WORK_ORDER_HOUR_PER_SHIFT:
            initial_plan_start += timedelta(hours=12)  # Move to the next shift
            cusum_max = df.loc[
                (df['Shift Start DateTime'] == initial_plan_start) & (df['ownergroup'] == department),
                'CUSUM Hours'
            ].max()
            cusum_max = cusum_max if pd.notna(cusum_max) else 0  # Reset for the new shift
            
            print("=================================================")
            print(f"New Shift Start: {initial_plan_start}, Updated CUSUM for {department}: {cusum_max}")

        # Assign values to the dataframe
        df.loc[index, 'Actual Plan Start Date'] = initial_plan_start
        df.at[index, 'Actual Plan End Date'] = initial_plan_start + timedelta(hours=df.at[index, 'Duration (Min)'] / 60)
        
        shift_start, shift_end = get_shift_start_end(df.at[index, 'Actual Plan End Date'])
        df.at[index, 'Shift Start DateTime'] = pd.to_datetime(shift_start)
        df.at[index, 'Shift End DateTime'] = pd.to_datetime(shift_end)
        df.at[index, 'Crew'] = get_crew_letter(shift_start)
        df.at[index, 'CUSUM Hours'] = cusum_max + (df.at[index, 'Duration (Min)'] / 60)
        df.at[index, 'Groupby'] = 'Others'

        print(f"{index} | {df.at[index, 'wonum']} | {df.at[index, 'Equipment Description']} | "
              f"{df.at[index, 'Shift Start DateTime']} | {df.at[index, 'Duration (Min)']} | "
              f"{df.at[index, 'Crew']} | {cusum_max} | {df.at[index, 'CUSUM Hours']}")
# =============== Get equipment master data ===============
BASE_URL_ASSET = "https://ems-lgensol.singlex.com/maximo/oslc/os/MXASSET"
HEADERS = {"Accept": "application/json"}

params = {
    "oslc.where": 'siteid="UTIL.GM" and status="ACTIVE"',
    "oslc.select": "assetnum,description",
    "apikey": API_KEY
}

# API Call (only once)
print("🔍 Requesting asset data from Maximo...")
try:
    response = requests.get(BASE_URL_ASSET, headers=HEADERS, params=params, verify=False)
    response.raise_for_status()
    data = response.json()

    # Prepare DB
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("""
        CREATE TABLE IF NOT EXISTS equipment_master (
            assetnum TEXT PRIMARY KEY,
            description TEXT
        )
    """)
    cursor.execute("DELETE FROM equipment_master")

    # Insert results into DB
    assets = data.get("rdfs:member", [])
    for asset in assets:
        assetnum = asset.get("spi:assetnum", "N/A")
        description = asset.get("spi:description", "N/A")
        cursor.execute(
            "INSERT OR REPLACE INTO equipment_master (assetnum, description) VALUES (?, ?)",
            (assetnum, description)
        )

    # Finalize
    conn.commit()
    conn.close()
    print(f"✅ {len(assets)} ACTIVE assets from UTIL.GM saved to {DB_PATH}.")

except requests.exceptions.RequestException as e:
    print(f"❌ Error fetching asset data: {e}")
except Exception as e:
    print(f"❌ Error processing asset data: {e}")

# =============== INNER JOIN assetnum to equipment description ===================
try:
    conn = sqlite3.connect(DB_PATH)

    df = pd.read_sql_query(f"SELECT * FROM {TABLE_NAME}", conn)

    # Step 1: Load equipment_master table
    equipment_master_df = pd.read_sql_query("SELECT assetnum, description FROM equipment_master", conn)
    equipment_master_df.rename(columns={'description': 'Equipment Description'}, inplace=True)
    equipment_master_df['Equipment Description'] = equipment_master_df['Equipment Description'].str.replace(' ‎', '', regex=False)
    df['description'] = df['description'].str.replace(' ‎', '', regex=False)

    # Step 2: Drop any existing Equipment Description to avoid "_x" suffix
    if 'Equipment Description' in df.columns:
        df.drop(columns=['Equipment Description'], inplace=True)

    # Step 3: Merge equipment descriptions
    df = df.merge(equipment_master_df, on='assetnum', how='left')

    # Step 4: Clean up ownergroup
    df['ownergroup'] = df['ownergroup'].str.replace('GM.UT.M', 'Mechanical')
    df['ownergroup'] = df['ownergroup'].str.replace('GM.UT.E', 'Electrical')
    df['ownergroup'] = df['ownergroup'].str.replace('GM.UT.B', 'Boiler')
    df['ownergroup'] = df['ownergroup'].str.replace('GM.UT.O', 'Operator')
    df['ownergroup'] = df['ownergroup'].str.replace('GM.UT.H', 'HVAC')

    # Step 5: Convert schedstart and schedfinish to datetime to Eastern timezone(YYYY-MM-DD HH:MM:SS)
    df['schedstart'] = pd.to_datetime(df['schedstart'], utc=True).dt.tz_convert('America/New_York').dt.tz_localize(None)
    df['schedfinish'] = pd.to_datetime(df['schedfinish'], utc=True).dt.tz_convert('America/New_York').dt.tz_localize(None)

    conn.close()
    print("✅ Equipment Description updated from equipment_master.")

except Exception as e:
    print(f"⚠️ Error updating Equipment Description: {e}")
    # Fallback if merge fails
    if 'Equipment Description' not in df.columns:
        df['Equipment Description'] = df['description']



# =====================SAVE data to Data.db as "TODO_WorkOrder" table====================="
try:
    conn = sqlite3.connect(DB_PATH)
    df.to_sql('TODO_WorkOrder', conn, if_exists='replace', index=False)
    conn.close()
    print(f"✅ Data saved to {DB_PATH} as 'TODO_WorkOrder' table with {len(df)} records.")
except Exception as e:
    print(f"❌ Error saving data: {e}")

print("🎉 Script execution completed successfully!")

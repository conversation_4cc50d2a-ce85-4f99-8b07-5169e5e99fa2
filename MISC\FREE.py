import requests
import os
import sqlite3
from datetime import datetime, timezone
import urllib3

# === CONFIG ===
MAXIMO_URL = "https://ems-lgensol.singlex.com/maximo/oslc/os/oslcwodetail"
API_KEY    = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
DB_FILE    = "data/Data.db"
TABLE_NAME = "OJT_WO_status"

# === LOAD LAST REFRESH DATE ===
def load_last_refresh_date():
    filename = "last_refresh_date_OJT.txt"
    if os.path.exists(filename):
        with open(filename, "r") as f:
            return f.read().strip()
    return "2000-01-01T00:00:00+00:00"  # fallback

def save_last_refresh_date(iso_str):
    with open("last_refresh_date_OJT.txt", "w") as f:
        f.write(iso_str)

LAST_REFRESH_DATE = load_last_refresh_date()
print(f"LAST REFRESH DATE in UTC: {LAST_REFRESH_DATE}\n")

# === HEADERS ===
headers = {
    "apikey": API_KEY,
    "Accept": "application/json"
}

# === WHERE CLAUSE & PARAMS ===
where_clause = (
    'spi_wm:istask=0 and '
    'spi_wm:woclass="WORKORDER" and '
    'spi_wm:siteid="UTIL.GM" and '
    'spi_wm:ownergroup="GM.UT.U" and '
    'spi:status!="CAN" and '
    f'spi_wm:statusdate>"{LAST_REFRESH_DATE}"'
)

params = {
    "oslc.where": where_clause,
    "oslc.select": "spi:wostatus,spi_wm:actuallabor,spi_wm:statusdate,spi_wm:jpnum,dcterms:identifier,dcterms:title",
    "lean": "1"
}

# === FIRE THE REQUEST ===
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

from urllib.parse import urlencode

# Manually build and print the full URL for debugging
full_url = f"{MAXIMO_URL}?{urlencode(params)}"
print(f"🌐 Request URL: {full_url}\n")

resp = requests.get(
    MAXIMO_URL,
    headers=headers,
    params=params,
    verify=False
)

if resp.status_code == 200:
    data = resp.json()
    print("✅ Data retrieved successfully from Maximo")

    records = data.get("member", [])
    print(f"🔍 {len(records)} work orders returned\n")

    # === Parse joined status history × labor entries ===
    status_rows = []
    for record in records:
        statusdate = record.get("statusdate")
        jpnum = record.get("jpnum")
        workorderid = record.get("identifier")
        wostatus_list = record.get("wostatus", [])
        labor_list   = record.get("actuallabor", [])
        description = record.get("title")

        labor_names = [
            lab.get("zlaborname") or lab.get("name")
            for lab in labor_list
            if lab.get("zlaborname") or lab.get("name")
        ]

        if not labor_names:
            # No labor — still include statusdate
            for status in wostatus_list:
                status_rows.append((
                    status.get("parent"),
                    status.get("changeby"),
                    status.get("changedate"),
                    status.get("status"),
                    None,        # laborname blank
                    None,
                    statusdate,
                    jpnum,
                    workorderid,
                    description
                ))
        else:
            for status in wostatus_list:
                for lab in labor_list:
                    laborname = lab.get("zlaborname") or lab.get("name")
                    assetnum = lab.get("assetnum")
                    if laborname:  # only append if name exists
                        status_rows.append((
                            status.get("parent"),
                            status.get("changeby"),
                            status.get("changedate"),
                            status.get("status"),
                            laborname,
                            assetnum,
                            statusdate,
                            jpnum,
                            workorderid,
                            description
                        ))


    print(f"📝 Parsed {len(status_rows)} wostatus entries\n")

    # === SAVE TO SQLITE ===
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()

    cursor.execute(f"DROP TABLE IF EXISTS {TABLE_NAME}")
    cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            parent TEXT,
            changeby TEXT,
            changedate TEXT,
            status TEXT,
            laborname TEXT,
            assetnum TEXT,
            statusdate TEXT,
            jpnum TEXT,
            workorderid TEXT,
            description TEXT
        )
    """)

    cursor.executemany(f"""
        INSERT INTO {TABLE_NAME} (
            parent, changeby, changedate, status, laborname, assetnum, statusdate, jpnum, workorderid, description
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, status_rows)

    conn.commit()
    conn.close()

    print(f"📦 Saved {len(status_rows)} rows to '{TABLE_NAME}' in '{DB_FILE}'")

    # # === UPDATE REFRESH DATE ===
    # new_refresh_date = datetime.now(timezone.utc).replace(microsecond=0).isoformat()
    # save_last_refresh_date(new_refresh_date)
    # print(f"🕒 Updated last refresh date to: {new_refresh_date} (UTC)")

else:
    print(f"❌ Request failed: {resp.status_code}")
    print(resp.text)
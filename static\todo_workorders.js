document.addEventListener('DOMContentLoaded', function() {
    let allWorkOrders = [];
    
    // Initialize Gantt chart
    gantt.config.date_format = "%Y-%m-%d %H:%i";
    gantt.config.scale_unit = "day";
    gantt.config.date_scale = "%d %M";
    gantt.config.scale_height = 50;
    gantt.config.row_height = 30;
    gantt.config.bar_height = 20;
    gantt.config.grid_width = 400;
    gantt.config.fit_tasks = true;
    gantt.config.auto_scheduling = false;
    gantt.config.readonly = true;

    // Configure columns
    gantt.config.columns = [
        {name: "id", label: "Work Order", width: 600, tree: true, template: function(task) {
            return task.work_order_number || task.id || 'N/A';
        }}
    ];

    // Custom task styling based on status and crew
    gantt.templates.task_class = function(start, end, task) {
        let classes = [];

        // Add status class
        if (task.status) {
            classes.push('status_' + task.status);
        }

        // Add crew class for additional styling if needed
        if (task.crew) {
            classes.push('crew_' + task.crew);
        }

        return classes.join(' ');
    };

    // Function to get crew color - ensure this is defined before it's used
    function getCrewColor(crew) {
        const colors = {
            'A': '#6b007b',    // Red
            'B': '#118dff',    // Teal
            'C': '#e66c37',    // Blue
            'D': '#12239e',    // Green
        };

        // Handle empty or null crew values
        if (!crew || crew === '') {
            return '#CCCCCC';  // Gray for unassigned
        }

        // If crew not in predefined colors, generate a hash-based color
        if (!colors[crew]) {
            let hash = 0;
            for (let i = 0; i < crew.length; i++) {
                hash = crew.charCodeAt(i) + ((hash << 5) - hash);
            }
            const hue = Math.abs(hash) % 360;
            return `hsl(${hue}, 70%, 70%)`;
        }

        return colors[crew];
    }

    // Custom task color template based on crew - ensure this is using the correct crew value
    gantt.templates.task_color = function(task) {
        console.log("Task crew:", task.crew, "Color:", getCrewColor(task.crew)); // Debug log
        return getCrewColor(task.crew);
    };

    // Add CSS for crew-specific styling
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .gantt_task_line.crew_A { background-color: #FF6B6B !important; }
        .gantt_task_line.crew_B { background-color: #4ECDC4 !important; }
        .gantt_task_line.crew_C { background-color: #45B7D1 !important; }
        .gantt_task_line.crew_D { background-color: #96CEB4 !important; }
    `;
    document.head.appendChild(styleElement);

    // Custom task text template - show wonum instead of description
    gantt.templates.task_text = function(start, end, task) {
        // Use work_order_number if available, otherwise use text (description)
        const displayText = task.work_order_number || task.wonum || task.id || task.text;
        return displayText.length > 30 ? displayText.substring(0, 30) + '...' : displayText;
    };

    // Custom tooltip
    gantt.templates.tooltip_text = function(start, end, task) {
        return `
            <b>Work Order:</b> ${task.text}<br/>
            <b>Owner Group:</b> ${task.ownergroup}<br/>
            <b>JP Number:</b> ${task.jpnum}<br/>
            <b>Status:</b> ${task.status}<br/>
            <b>Start:</b> ${gantt.templates.tooltip_date_format(start)}<br/>
            <b>End:</b> ${gantt.templates.tooltip_date_format(end)}
        `;
    };

    // Initialize the gantt chart
    gantt.init("gantt-container");

    // Load initial data
    loadTodoWorkOrders();

    // Function to load TO-DO work orders
    function loadTodoWorkOrders() {
        fetch('/api/todo-workorders')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('Error loading work orders:', data.error);
                    showError('Failed to load work orders: ' + data.error);
                    return;
                }
                
                allWorkOrders = data.data || [];
                console.log('Loaded work orders:', allWorkOrders);
                
                // Apply current filters
                applyFilters();
            })
            .catch(error => {
                console.error('Error fetching work orders:', error);
                showError('Failed to fetch work orders. Please try again.');
            });
    }

    // Function to apply filters
    function applyFilters() {
        const ownerGroupFilter = document.getElementById('ownerGroupFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const startDateFilter = document.getElementById('startDateFilter').value;
        const endDateFilter = document.getElementById('endDateFilter').value;

        let filteredWorkOrders = allWorkOrders.filter(wo => {
            // Owner group filter
            if (ownerGroupFilter && wo.ownergroup !== ownerGroupFilter) {
                return false;
            }
            
            // Status filter
            if (statusFilter && wo.status !== statusFilter) {
                return false;
            }
            
            // Date range filter
            if (startDateFilter) {
                const woStartDate = new Date(wo.start_date);
                const filterStartDate = new Date(startDateFilter);
                if (woStartDate < filterStartDate) {
                    return false;
                }
            }
            
            if (endDateFilter) {
                const woEndDate = new Date(wo.end_date);
                const filterEndDate = new Date(endDateFilter + ' 23:59:59');
                if (woEndDate > filterEndDate) {
                    return false;
                }
            }
            
            return true;
        });

        // Update gantt chart with filtered data
        gantt.clearAll();
        gantt.parse({data: filteredWorkOrders});
        
        // Update the display
        updateSummary(filteredWorkOrders);
    }

    // Function to update summary information
    function updateSummary(workOrders) {
        const statusCounts = {
            pending: 0,
            inprogress: 0,
            completed: 0,
            overdue: 0
        };
        
        workOrders.forEach(wo => {
            if (statusCounts.hasOwnProperty(wo.status)) {
                statusCounts[wo.status]++;
            }
        });
        
        console.log('Summary:', statusCounts);
        // You can add UI elements to display these counts if needed
    }

    // Function to show error messages
    function showError(message) {
        const container = document.querySelector('.container');
        const existingError = container.querySelector('.error-message');
        
        if (existingError) {
            existingError.remove();
        }
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        
        const ganttContainer = document.getElementById('gantt-container');
        container.insertBefore(errorDiv, ganttContainer);
        
        // Auto-remove error after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }

    // Function to export Gantt chart to PDF
    function exportToPDF() {
        const exportBtn = document.querySelector('.export-btn');
        const originalText = exportBtn.innerHTML;

        // Disable button and show loading state
        exportBtn.disabled = true;
        exportBtn.innerHTML = '<span class="material-symbols-rounded">hourglass_empty</span>Exporting...';

        try {
            // Get current date for filename
            const now = new Date();
            const dateStr = now.toISOString().split('T')[0];
            const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
            const filename = `TODO_WorkOrders_${dateStr}_${timeStr}.pdf`;

            // Get the gantt container
            const ganttContainer = document.getElementById('gantt-container');

            // Configure html2canvas for high quality
            const options = {
                scale: 2, // Higher scale for better quality
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: ganttContainer.scrollWidth,
                height: ganttContainer.scrollHeight,
                scrollX: 0,
                scrollY: 0
            };

            html2canvas(ganttContainer, options).then(canvas => {
                try {
                    // Calculate dimensions for plotter-friendly format
                    const imgWidth = canvas.width;
                    const imgHeight = canvas.height;

                    // Use landscape orientation for better gantt chart display
                    // A3 size in landscape (420mm x 297mm) at 300 DPI
                    const pdfWidth = 420; // mm
                    const pdfHeight = 297; // mm

                    // Calculate scaling to fit the content
                    const scaleX = pdfWidth / (imgWidth * 0.264583); // Convert pixels to mm
                    const scaleY = pdfHeight / (imgHeight * 0.264583);
                    const scale = Math.min(scaleX, scaleY, 1); // Don't upscale

                    const finalWidth = (imgWidth * 0.264583) * scale;
                    const finalHeight = (imgHeight * 0.264583) * scale;

                    // Create PDF with high quality settings
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF({
                        orientation: 'landscape',
                        unit: 'mm',
                        format: 'a3',
                        compress: false // Don't compress for better quality
                    });

                    // Add title and metadata
                    pdf.setFontSize(16);
                    pdf.text('TO-DO Work Orders Gantt Chart', 20, 20);

                    pdf.setFontSize(10);
                    const exportDate = new Date().toLocaleString();
                    pdf.text(`Generated: ${exportDate}`, 20, 30);

                    // Add filter information
                    const ownerGroup = document.getElementById('ownerGroupFilter').value;
                    const status = document.getElementById('statusFilter').value;
                    const startDate = document.getElementById('startDateFilter').value;
                    const endDate = document.getElementById('endDateFilter').value;

                    let filterText = 'Filters: ';
                    if (ownerGroup) filterText += `Owner Group: ${ownerGroup}, `;
                    if (status) filterText += `Status: ${status}, `;
                    if (startDate) filterText += `Start: ${startDate}, `;
                    if (endDate) filterText += `End: ${endDate}`;
                    if (filterText.endsWith(', ')) filterText = filterText.slice(0, -2);

                    pdf.text(filterText, 20, 40);

                    // Add the gantt chart image
                    const imgData = canvas.toDataURL('image/png', 1.0); // Maximum quality
                    const xOffset = (pdfWidth - finalWidth) / 2;
                    const yOffset = 50; // Leave space for header

                    pdf.addImage(imgData, 'PNG', xOffset, yOffset, finalWidth, finalHeight, '', 'FAST');

                    // Save the PDF
                    pdf.save(filename);

                    console.log('PDF exported successfully');

                } catch (pdfError) {
                    console.error('Error creating PDF:', pdfError);
                    alert('Error creating PDF. Please try again.');
                }

                // Re-enable button
                exportBtn.disabled = false;
                exportBtn.innerHTML = originalText;

            }).catch(canvasError => {
                console.error('Error capturing canvas:', canvasError);
                alert('Error capturing chart. Please try again.');

                // Re-enable button
                exportBtn.disabled = false;
                exportBtn.innerHTML = originalText;
            });

        } catch (error) {
            console.error('Error during export:', error);
            alert('Error during export. Please try again.');

            // Re-enable button
            exportBtn.disabled = false;
            exportBtn.innerHTML = originalText;
        }
    }

    // Make functions globally available
    window.loadTodoWorkOrders = loadTodoWorkOrders;
    window.applyFilters = applyFilters;
    window.exportToPDF = exportToPDF;

    // Set default date filters (last 30 days to next 90 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    const ninetyDaysFromNow = new Date(today.getTime() + (90 * 24 * 60 * 60 * 1000));

    document.getElementById('startDateFilter').value = thirtyDaysAgo.toISOString().split('T')[0];
    document.getElementById('endDateFilter').value = ninetyDaysFromNow.toISOString().split('T')[0];
});








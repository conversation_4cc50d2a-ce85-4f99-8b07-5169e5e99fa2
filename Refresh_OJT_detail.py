import requests
import os
import sqlite3
from datetime import datetime, timezone
import urllib3
from zoneinfo import ZoneInfo
from urllib.parse import urlencode

# === CONFIG ===
MAXIMO_URL = "https://ems-lgensol.singlex.com/maximo/oslc/os/oslcwodetail"
API_KEY    = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
DB_FILE    = "data/DataNew.db"
TABLE_NAME = "OJT_WO_status"

# === TIMEZONE CONVERSION FUNCTION ===
def convert_utc_to_eastern(utc_timestamp_str):
    if not utc_timestamp_str:
        return None
    try:
        utc_dt = datetime.fromisoformat(utc_timestamp_str.replace('Z', '+00:00'))
        eastern_tz = ZoneInfo('America/New_York')
        eastern_dt = utc_dt.astimezone(eastern_tz)
        return eastern_dt.isoformat()
    except Exception as e:
        print(f"⚠️ Error converting timestamp {utc_timestamp_str}: {e}")
        return utc_timestamp_str

# === LOAD/SAVE LAST REFRESH DATE ===
def load_last_refresh_date():
    filename = "last_refresh_date_OJT.txt"
    if os.path.exists(filename):
        with open(filename, "r") as f:
            return f.read().strip()
    return "2000-01-01T00:00:00+00:00"

def save_last_refresh_date(iso_str):
    with open("last_refresh_date_OJT.txt", "w") as f:
        f.write(iso_str)

# === FILTER FUNCTION TO REMOVE CAN STATUS ROWS ===
def filter_can_status_rows(status_rows):
    """
    Remove all rows where status = 'CAN' and also remove all rows
    that share the same 'parent' value with any CAN status row.
    """
    # Find all parent values that have a CAN status
    can_parents = set()
    for row in status_rows:
        parent, changeby, changedate, status, laborname, assetnum, statusdate, jpnum, workorderid, description = row
        if status == "CAN":
            can_parents.add(parent)

    # Filter out all rows with parents that have CAN status
    filtered_rows = []
    removed_count = 0

    for row in status_rows:
        parent = row[0]  # parent is the first element in the tuple
        if parent not in can_parents:
            filtered_rows.append(row)
        else:
            removed_count += 1

    print(f"🚫 Found {len(can_parents)} parent(s) with CAN status")
    print(f"🗑️ Removed {removed_count} rows (including CAN status and related rows)")
    print(f"✅ Keeping {len(filtered_rows)} rows after filtering\n")

    return filtered_rows

# === FUNCTION TO KEEP ONLY LATEST STATUS FOR EACH UNIQUE COMBINATION ===
def keep_latest_status_per_unique_combination(status_rows):
    """
    For each unique combination of (status, laborname, assetnum), keep only the record with the latest changedate.
    This ensures that each person (laborname) can only have one instance of each status for each asset.
    """
    from collections import defaultdict

    # Group records by (status, laborname, assetnum) combination
    status_groups = defaultdict(list)

    for row in status_rows:
        parent, changeby, changedate, status, laborname, assetnum, statusdate, jpnum, workorderid, description = row
        # Only group if laborname and assetnum are not None
        if laborname and assetnum:
            key = (status, laborname, assetnum)
            status_groups[key].append(row)
        else:
            # If laborname or assetnum is None, treat as unique (don't group)
            status_groups[len(status_groups)] = [row]

    # For each group, keep only the record with the latest changedate
    latest_records = []

    for key, records in status_groups.items():
        if len(records) > 1:
            # Sort by changedate (latest first) and take the first one
            latest_record = max(records, key=lambda x: x[2] if x[2] else "")
            latest_records.append(latest_record)

            # Log what we're keeping vs removing
            if isinstance(key, tuple) and len(key) == 3:
                status, laborname, assetnum = key
                print(f"   📝 {status} - {laborname} - {assetnum}: {len(records)} records (keeping latest)")
        else:
            # Only one record, keep it
            latest_records.append(records[0])

    original_count = len(status_rows)
    deduplicated_count = len(latest_records)
    removed_count = original_count - deduplicated_count

    print(f"📊 Deduplication results:")
    print(f"   Original records: {original_count}")
    print(f"   Unique (status, laborname, assetnum) combinations: {deduplicated_count}")
    print(f"   Duplicate records removed: {removed_count}")
    print(f"✅ Keeping only latest changedate for each unique combination\n")

    return latest_records

# === FUNCTION TO CLEAN DATABASE OF OLDER WORK ORDER CYCLES ===
def clean_database_duplicates(new_records, conn):
    """
    After inserting new records, clean the database to remove older work order cycles.
    When a new DRAFT status comes in for a (laborname, assetnum) combination,
    remove ALL rows from the older parent (complete work order cycle).
    """
    print("🧹 Cleaning database for older work order cycles...")

    cursor = conn.cursor()

    # Find new DRAFT records that were just inserted
    new_draft_combinations = []
    for record in new_records:
        parent, changeby, changedate, status, laborname, assetnum, statusdate, jpnum, workorderid, description = record
        if status == 'DRAFT' and laborname and assetnum:
            new_draft_combinations.append((laborname, assetnum, parent, changedate))

    if not new_draft_combinations:
        print("✅ No new DRAFT records to process")
        return

    print(f"🔍 Found {len(new_draft_combinations)} new DRAFT records to process")

    parents_to_remove = set()

    for laborname, assetnum, new_parent, new_changedate in new_draft_combinations:
        print(f"   📝 Processing DRAFT for {laborname} - {assetnum} (new parent: {new_parent})")

        # Find all existing DRAFT records for this (laborname, assetnum) combination
        cursor.execute(f"""
            SELECT parent, changedate
            FROM {TABLE_NAME}
            WHERE status = 'DRAFT' AND laborname = ? AND assetnum = ? AND parent != ?
            ORDER BY changedate DESC
        """, (laborname, assetnum, new_parent))

        existing_drafts = cursor.fetchall()

        for existing_parent, existing_changedate in existing_drafts:
            # Compare dates - if new DRAFT is later, mark old parent for removal
            if new_changedate > existing_changedate:
                parents_to_remove.add(existing_parent)
                print(f"      🗑️ Marking parent {existing_parent} for removal (older: {existing_changedate} < newer: {new_changedate})")
            else:
                # If existing DRAFT is newer, mark new parent for removal
                parents_to_remove.add(new_parent)
                print(f"      🗑️ Marking parent {new_parent} for removal (older: {new_changedate} < newer: {existing_changedate})")

    if parents_to_remove:
        print(f"\n🗑️ Removing all records for {len(parents_to_remove)} older parent(s)...")

        # Count records before deletion
        cursor.execute(f"SELECT COUNT(*) FROM {TABLE_NAME}")
        total_before = cursor.fetchone()[0]

        # Remove all records for the older parents
        for parent in parents_to_remove:
            cursor.execute(f"""
                DELETE FROM {TABLE_NAME} WHERE parent = ?
            """, (parent,))
            print(f"      ✅ Removed all records for parent {parent}")

        conn.commit()

        # Count records after deletion
        cursor.execute(f"SELECT COUNT(*) FROM {TABLE_NAME}")
        total_after = cursor.fetchone()[0]

        deleted_count = total_before - total_after
        print(f"✅ Database cleanup completed")
        print(f"📊 Removed {deleted_count} records from {len(parents_to_remove)} older work order cycles")
        print(f"📊 Final record count: {total_after}")
    else:
        print("✅ No older work order cycles to remove")

    print()

# === MAIN REFRESH FUNCTION ===
def refresh():
    print("🔄 Starting OJT refresh process...\n")

    LAST_REFRESH_DATE = load_last_refresh_date()
    print(f"LAST REFRESH DATE in UTC: {LAST_REFRESH_DATE}\n")

    headers = {
        "apikey": API_KEY,
        "Accept": "application/json"
    }

    where_clause = (
        'spi_wm:istask=0 and '
        'spi_wm:woclass="WORKORDER" and '
        'spi_wm:siteid="UTIL.GM" and '
        'spi_wm:ownergroup="GM.UT.U" and '
        f'spi_wm:statusdate>"{LAST_REFRESH_DATE}"'
    )

    params = {
        "oslc.where": where_clause,
        "oslc.select": "spi:wostatus,spi_wm:actuallabor,spi_wm:statusdate,spi_wm:jpnum,dcterms:identifier,dcterms:title",
        "lean": "1"
    }

    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    full_url = f"{MAXIMO_URL}?{urlencode(params)}"
    print(f"🌐 Request URL: {full_url}\n")

    resp = requests.get(MAXIMO_URL, headers=headers, params=params, verify=False)

    if resp.status_code != 200:
        print(f"❌ Request failed: {resp.status_code}")
        print(resp.text)
        return

    data = resp.json()
    records = data.get("member", [])
    print(f"✅ Data retrieved successfully from Maximo")
    print(f"🔍 {len(records)} work orders returned\n")

    status_rows = []
    for record in records:
        statusdate = convert_utc_to_eastern(record.get("statusdate"))
        jpnum = record.get("jpnum")
        workorderid = record.get("identifier")
        wostatus_list = record.get("wostatus", [])
        labor_list = record.get("actuallabor", [])
        description = record.get("title")

        labor_names = [lab.get("zlaborname") or lab.get("name") for lab in labor_list if lab.get("zlaborname") or lab.get("name")]

        if not labor_names:
            for status in wostatus_list:
                changedate = convert_utc_to_eastern(status.get("changedate"))
                status_rows.append((
                    status.get("parent"),
                    status.get("changeby"),
                    changedate,
                    status.get("status"),
                    None,
                    None,
                    statusdate,
                    jpnum,
                    workorderid,
                    description
                ))
        else:
            for status in wostatus_list:
                changedate = convert_utc_to_eastern(status.get("changedate"))
                for lab in labor_list:
                    laborname = lab.get("zlaborname") or lab.get("name")
                    assetnum = lab.get("assetnum")
                    if laborname:
                        status_rows.append((
                            status.get("parent"),
                            status.get("changeby"),
                            changedate,
                            status.get("status"),
                            laborname,
                            assetnum,
                            statusdate,
                            jpnum,
                            workorderid,
                            description
                        ))

    print(f"📝 Parsed {len(status_rows)} wostatus entries (timestamps converted to Eastern)")

    # Filter out CAN status rows and related rows
    status_rows = filter_can_status_rows(status_rows)

    # Keep only the latest changedate for each unique combination
    status_rows = keep_latest_status_per_unique_combination(status_rows)

    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()

    cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            parent TEXT,
            changeby TEXT,
            changedate TEXT,
            status TEXT,
            laborname TEXT,
            assetnum TEXT,
            statusdate TEXT,
            jpnum TEXT,
            workorderid TEXT,
            description TEXT
        )
    """)

    cursor.executemany(f"""
        INSERT INTO {TABLE_NAME} (
            parent, changeby, changedate, status, laborname, assetnum, statusdate, jpnum, workorderid, description
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, status_rows)

    print(f"📦 Inserted {len(status_rows)} new rows to '{TABLE_NAME}' in '{DB_FILE}'")

    # Clean database of duplicate (status, laborname, assetnum) combinations
    clean_database_duplicates(status_rows, conn)

    # Clean up the entire database - remove all rows where parent has ANY CAN status
    print("🧹 Cleaning up database - removing all work orders with CAN status...")
    
    # First, find all parents that have CAN status in the entire database
    cursor.execute(f"""
        SELECT DISTINCT parent FROM {TABLE_NAME} WHERE status = 'CAN'
    """)
    can_parents_db = [row[0] for row in cursor.fetchall()]
    
    if can_parents_db:
        print(f"🚫 Found {len(can_parents_db)} parent(s) with CAN status in database")
        
        # Count rows before deletion
        cursor.execute(f"SELECT COUNT(*) FROM {TABLE_NAME}")
        total_before = cursor.fetchone()[0]
        
        # Delete all rows with these parent values
        placeholders = ','.join(['?' for _ in can_parents_db])
        cursor.execute(f"""
            DELETE FROM {TABLE_NAME} WHERE parent IN ({placeholders})
        """, can_parents_db)
        
        # Count rows after deletion
        cursor.execute(f"SELECT COUNT(*) FROM {TABLE_NAME}")
        total_after = cursor.fetchone()[0]
        
        deleted_count = total_before - total_after
        print(f"🗑️ Deleted {deleted_count} rows from database (all statuses for parents with CAN)")
        print(f"📊 Database now has {total_after} total rows")
    else:
        cursor.execute(f"SELECT COUNT(*) FROM {TABLE_NAME}")
        total_rows = cursor.fetchone()[0]
        print(f"✅ No CAN status found in database")
        print(f"📊 Database has {total_rows} total rows")

    conn.commit()
    conn.close()

    new_refresh_date = datetime.now(timezone.utc).replace(microsecond=0).isoformat()
    save_last_refresh_date(new_refresh_date)
    print(f"🕒 Updated last refresh date to: {new_refresh_date} (UTC)")

# Allow standalone execution
if __name__ == '__main__':
    refresh()
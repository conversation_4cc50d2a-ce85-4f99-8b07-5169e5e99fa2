{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["\n", "# Ultium Cells Facility Management Application - Flex Chart\n", "\n", "## Project.py\n", "\n", "The `project.py` file is the main entry point for the Flask application. It contains the following key components:\n", "\n", "### Flask Application Setup\n", "\n", "The Flask application is initialized with the following line:\n", "\n", "```python\n", "app = Flask(__name__)\n", "```\n", "\n", "The Database is created and connected to the application with the following line:\n", "\n", "```python\n", "DB_PATH = os.path.join('data', 'Data.db')\n", "os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Database Structure\n", "\n", "### Check if the database file exists"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Database path: c:\\Users\\<USER>\\Desktop\\MyWebsite\\UltiumApp\\Data\\DataNew.db\n", "\n", "Database file exists: Data\\DataNew.db\n"]}], "source": ["import sqlite3\n", "import os\n", "\n", "# Database path - make sure this matches the path in project.py\n", "DB_PATH = os.path.join('Data', 'DataNew.db')\n", "print(f\"Database path: {os.path.abspath(DB_PATH)}\")\n", "\n", "# Check if the database file exists\n", "if os.path.exists(DB_PATH):\n", "    print(f\"\\nDatabase file exists: {DB_PATH}\")\n", "else:\n", "    print(f\"Database file does not exist: {DB_PATH}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### List the tables in the database and their columns"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tables in the database:\n", "  sqlite_sequence\n", "  WorkOrder_temp\n", "  Authorization\n", "  AppData\n", "  equipment_master\n", "  WorkOrder_Draft\n", "  TODO_WorkOrder\n", "  work_orders\n", "  OJT_raw_data\n", "  WorkOrder_Status\n", "  OJT_WorkOrder\n", "  <PERSON><PERSON><PERSON><PERSON>\n", "  backup_OJT_WO_status\n", "  OJT_WO_status\n", "\n", "OrgChart table columns:\n", "id, parentId, name, title, position, department, shift, phone, image, team\n"]}], "source": ["def print_database_schema():\n", "    # Connect to the SQLite database\n", "    conn = sqlite3.connect(DB_PATH)\n", "    cursor = conn.cursor()\n", "    \n", "    # Get list of tables\n", "    cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table';\")\n", "    tables = cursor.fetchall()\n", "    \n", "    print(\"Tables in the database:\")\n", "    for table in tables:\n", "        print(f\"  {table[0]}\")\n", "    \n", "    # Close the connection\n", "    conn.close()\n", "\n", "# Print the database schema\n", "print_database_schema()\n", "table_choice = 'OrgChart'\n", "\n", "def print_table_schema():\n", "    # Connect to the SQLite database\n", "    conn = sqlite3.connect(DB_PATH)\n", "    cursor = conn.cursor()\n", "    \n", "    try:\n", "        print(f\"\\n{table_choice} table columns:\")\n", "        cursor.execute(f\"PRAGMA table_info('{table_choice}')\")\n", "        schema = cursor.fetchall()\n", "        \n", "        if not schema:\n", "            print(\"No columns found or table does not exist.\")\n", "            return\n", "\n", "        # Extract and print column names in comma-separated format\n", "        column_names = [column[1] for column in schema]\n", "        print(\", \".join(column_names))\n", "        \n", "    except sqlite3.OperationalError as e:\n", "        print(f\"Error: {e}\")\n", "        \n", "    finally:\n", "        conn.close()\n", "\n", "# Print the table columns\n", "print_table_schema()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tables in the database:\n", "  \n", "- sqlite_sequence: internal SQLite table that helps implement the AUTOINCREMENT feature.  \n", "- WorkOrder_temp: Empty table with same schema as WorkOrder  \n", "- Authorization: List of users and their access level\n", "- AppData: Data points to export to Flex Chart (Getting the Latestest status for each work order)\n", "- OrgChart: Organization Chart. This data is also exported to the Flex Chart\n", "- WorkOrder: List of Work Orders and Status History for Gantt Chart\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Print head of Table"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "First 30 rows of OrgChart table:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>parentId</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>position</th>\n", "      <th>department</th>\n", "      <th>shift</th>\n", "      <th>phone</th>\n", "      <th>image</th>\n", "      <th>team</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>Ultium Cells Facility</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/C560BAQEY...</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td>Sr. Facility Manager</td>\n", "      <td>Senior Manager</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/C4E03AQHi...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Facility Manager</td>\n", "      <td>Manager</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/C4E03AQFo...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Engineer II</td>\n", "      <td>Reliability Engineer</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>************</td>\n", "      <td>https://media.licdn.com/dms/image/v2/D4E03AQE5...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Sr. Engineer</td>\n", "      <td>Electrical Engineer</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Engineer II</td>\n", "      <td>HVAC Specialist</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Chemical Engineer</td>\n", "      <td>Sr. Engineer</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/D4E03AQE_...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Chemical Engineer</td>\n", "      <td>Engineer I</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Night Shift</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>9</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Mechanical FCE</td>\n", "      <td>Professional</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Mechanical Engineer</td>\n", "      <td>Engineer I</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/D4D03AQHp...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>11</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Project Engineer</td>\n", "      <td>Engineer II</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Facility Planner</td>\n", "      <td>Specialist II</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>************</td>\n", "      <td>https://media.licdn.com/dms/image/v2/C5603AQFK...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>13</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>General <PERSON><PERSON><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>330-980-8851</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>14</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>D-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Operation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>15</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist II</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>A-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Operation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>16</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>B-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Technician</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>17</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>D-Crew</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/D5603AQFZ...</td>\n", "      <td>HVAC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>18</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>C-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>HVAC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>19</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>D-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Technician</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>20</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>A-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Technician</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>21</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>B-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Technician</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>22</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>C-Crew</td>\n", "      <td>330-883-6445</td>\n", "      <td>None</td>\n", "      <td>Technician</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>23</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>B-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>HVAC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>24</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist II</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>C-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Operation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>25</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist II</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>B-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Operation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>27</td>\n", "      <td>15</td>\n", "      <td><PERSON></td>\n", "      <td>Operator</td>\n", "      <td>Crew Leader</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>A-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Operation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>28</td>\n", "      <td>15</td>\n", "      <td><PERSON></td>\n", "      <td>Operator</td>\n", "      <td>Associate</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>A-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Operation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>29</td>\n", "      <td>15</td>\n", "      <td><PERSON></td>\n", "      <td>Operator</td>\n", "      <td>Associate</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>A-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Operation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>30</td>\n", "      <td>15</td>\n", "      <td><PERSON></td>\n", "      <td>Operator</td>\n", "      <td>Associate</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>A-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Operation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>31</td>\n", "      <td>15</td>\n", "      <td><PERSON></td>\n", "      <td>Operator</td>\n", "      <td>Associate</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>A-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Operation</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id parentId                   name                 title  \\\n", "0    1     None  Ultium Cells Facility                  None   \n", "1    2        1            <PERSON>. Facility Manager   \n", "2    3        2             Heath Ruth      Facility Manager   \n", "3    4        2            <PERSON> II   \n", "4    5        2          <PERSON>   \n", "5    6        2         <PERSON> II   \n", "6    7        2       <PERSON>     Chemical Engineer   \n", "7    8        2          <PERSON>sler     Chemical Engineer   \n", "8    9        2         <PERSON><PERSON> Chan Kim        Mechanical FCE   \n", "9   10        2           <PERSON>   Mechanical Engineer   \n", "10  11        2           <PERSON>      Project Engineer   \n", "11  12        3               Bob Gaps      Facility Planner   \n", "12  13        3            <PERSON>   \n", "13  14        3         <PERSON>   \n", "14  15        3          <PERSON> II   \n", "15  16        3         <PERSON>   \n", "16  17        3      <PERSON><PERSON>field          Specialist I   \n", "17  18        3         <PERSON> I   \n", "18  19        3         <PERSON> I   \n", "19  20        3         <PERSON><PERSON> I   \n", "20  21        3        <PERSON><PERSON><PERSON> I   \n", "21  22        3         <PERSON>   \n", "22  23        3           <PERSON>   \n", "23  24        3           <PERSON> II   \n", "24  25        3           <PERSON> Chamber         <PERSON> II   \n", "25  27       15      <PERSON> Fetterolf              Operator   \n", "26  28       15          <PERSON>tor   \n", "27  29       15      Desire Washington              Operator   \n", "28  30       15         <PERSON>              Operator   \n", "29  31       15            <PERSON>   \n", "\n", "                position            department        shift         phone  \\\n", "0                   None  Facility Maintenance         None          None   \n", "1         Senior Manager  Facility Maintenance    Day Shift          None   \n", "2                Manager  Facility Maintenance    Day Shift          None   \n", "3   Reliability Engineer  Facility Maintenance    Day Shift  ************   \n", "4    Electrical Engineer  Facility Maintenance    Day Shift          None   \n", "5        HVAC Specialist  Facility Maintenance    Day Shift          None   \n", "6           Sr. Engineer  Facility Maintenance    Day Shift          None   \n", "7             Engineer I  Facility Maintenance  Night Shift          None   \n", "8          Professional   Facility Maintenance    Day Shift          None   \n", "9             Engineer I  Facility Maintenance    Day Shift          None   \n", "10           Engineer II  Facility Maintenance    Day Shift          None   \n", "11         Specialist II  Facility Maintenance    Day Shift  ************   \n", "12         Specialist I   Facility Maintenance    Day Shift  330-980-8851   \n", "13            Supervisor  Facility Maintenance       D-Crew          None   \n", "14            Supervisor  Facility Maintenance       A-Crew          None   \n", "15            Supervisor  Facility Maintenance       B-Crew          None   \n", "16            Supervisor  Facility Maintenance       D-Crew          None   \n", "17            Supervisor  Facility Maintenance       C-Crew          None   \n", "18            Supervisor  Facility Maintenance       D-Crew          None   \n", "19            Supervisor  Facility Maintenance       A-Crew          None   \n", "20            Supervisor  Facility Maintenance       B-Crew          None   \n", "21            Supervisor  Facility Maintenance       C-Crew  330-883-6445   \n", "22            Supervisor  Facility Maintenance       B-Crew          None   \n", "23            Supervisor  Facility Maintenance       C-Crew          None   \n", "24            Supervisor  Facility Maintenance       B-Crew          None   \n", "25           Crew Leader  Facility Maintenance       A-Crew          None   \n", "26             Associate  Facility Maintenance       A-Crew          None   \n", "27             Associate  Facility Maintenance       A-Crew          None   \n", "28             Associate  Facility Maintenance       A-Crew          None   \n", "29             Associate  Facility Maintenance       A-Crew          None   \n", "\n", "                                                image        team  \n", "0   https://media.licdn.com/dms/image/v2/C560BAQEY...        None  \n", "1   https://media.licdn.com/dms/image/v2/C4E03AQHi...       Other  \n", "2   https://media.licdn.com/dms/image/v2/C4E03AQFo...       Other  \n", "3   https://media.licdn.com/dms/image/v2/D4E03AQE5...       Other  \n", "4                                                None       Other  \n", "5                                                None       Other  \n", "6   https://media.licdn.com/dms/image/v2/D4E03AQE_...       Other  \n", "7                                                None       Other  \n", "8                                                None       Other  \n", "9   https://media.licdn.com/dms/image/v2/D4D03AQHp...       Other  \n", "10                                               None       Other  \n", "11  https://media.licdn.com/dms/image/v2/C5603AQFK...       Other  \n", "12                                               None       Other  \n", "13                                               None   Operation  \n", "14                                               None   Operation  \n", "15                                               None  Technician  \n", "16  https://media.licdn.com/dms/image/v2/D5603AQFZ...        HVAC  \n", "17                                               None        HVAC  \n", "18                                               None  Technician  \n", "19                                               None  Technician  \n", "20                                               None  Technician  \n", "21                                               None  Technician  \n", "22                                               None        HVAC  \n", "23                                               None   Operation  \n", "24                                               None   Operation  \n", "25                                               None   Operation  \n", "26                                               None   Operation  \n", "27                                               None   Operation  \n", "28                                               None   Operation  \n", "29                                               None   Operation  "]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["def display_table_head(table_name, n=5):\n", "    # Connect to the SQLite database\n", "    conn = sqlite3.connect(DB_PATH)\n", "    \n", "    # Set the number of rows to display\n", "    n = 30\n", "    \n", "    try:\n", "        # Import pandas if not already imported\n", "        import pandas as pd\n", "        \n", "        # Read the first n rows from the table\n", "        query = f\"SELECT * FROM {table_name} LIMIT {n}\"\n", "        df = pd.read_sql_query(query, conn)\n", "        \n", "        # Display the dataframe\n", "        print(f\"\\nFirst {n} rows of {table_name} table:\")\n", "        return df.head(n)\n", "        \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "    finally:\n", "        # Close the connection\n", "        conn.close()\n", "\n", "# Display the head of the selected table\n", "display_table_head(table_choice)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Exported data to OrgChart_export.xlsx\n", "\n", "First 20 rows of OrgChart table:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>parentId</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>position</th>\n", "      <th>department</th>\n", "      <th>shift</th>\n", "      <th>phone</th>\n", "      <th>image</th>\n", "      <th>team</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>Ultium Cells Facility</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/C560BAQEY...</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td>Sr. Facility Manager</td>\n", "      <td>Senior Manager</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/C4E03AQHi...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Facility Manager</td>\n", "      <td>Manager</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/C4E03AQFo...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Engineer II</td>\n", "      <td>Reliability Engineer</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>************</td>\n", "      <td>https://media.licdn.com/dms/image/v2/D4E03AQE5...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Sr. Engineer</td>\n", "      <td>Electrical Engineer</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Engineer II</td>\n", "      <td>HVAC Specialist</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Chemical Engineer</td>\n", "      <td>Sr. Engineer</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/D4E03AQE_...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Chemical Engineer</td>\n", "      <td>Engineer I</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Night Shift</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>9</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Mechanical FCE</td>\n", "      <td>Professional</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Mechanical Engineer</td>\n", "      <td>Engineer I</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/D4D03AQHp...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>11</td>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>Project Engineer</td>\n", "      <td>Engineer II</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Facility Planner</td>\n", "      <td>Specialist II</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>************</td>\n", "      <td>https://media.licdn.com/dms/image/v2/C5603AQFK...</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>13</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>General <PERSON><PERSON><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>Day Shift</td>\n", "      <td>330-980-8851</td>\n", "      <td>None</td>\n", "      <td>Other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>14</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>D-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Operation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>15</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist II</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>A-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Operation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>16</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>B-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Technician</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>17</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>D-Crew</td>\n", "      <td>None</td>\n", "      <td>https://media.licdn.com/dms/image/v2/D5603AQFZ...</td>\n", "      <td>HVAC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>18</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>C-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>HVAC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>19</td>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>D-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Technician</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>20</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Specialist I</td>\n", "      <td>Supervisor</td>\n", "      <td>Facility Maintenance</td>\n", "      <td>A-Crew</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Technician</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id parentId                   name                 title  \\\n", "0    1     None  Ultium Cells Facility                  None   \n", "1    2        1            <PERSON>. Facility Manager   \n", "2    3        2             Heath Ruth      Facility Manager   \n", "3    4        2            <PERSON> II   \n", "4    5        2          <PERSON>   \n", "5    6        2         <PERSON> II   \n", "6    7        2       <PERSON>     Chemical Engineer   \n", "7    8        2          <PERSON>sler     Chemical Engineer   \n", "8    9        2         <PERSON><PERSON> Chan Kim        Mechanical FCE   \n", "9   10        2           <PERSON>   Mechanical Engineer   \n", "10  11        2           <PERSON>      Project Engineer   \n", "11  12        3               Bob Gaps      Facility Planner   \n", "12  13        3            <PERSON>   \n", "13  14        3         <PERSON>   \n", "14  15        3          <PERSON> II   \n", "15  16        3         <PERSON>   \n", "16  17        3      <PERSON><PERSON>field          Specialist I   \n", "17  18        3         <PERSON> I   \n", "18  19        3         <PERSON> I   \n", "19  20        3         <PERSON><PERSON> I   \n", "\n", "                position            department        shift         phone  \\\n", "0                   None  Facility Maintenance         None          None   \n", "1         Senior Manager  Facility Maintenance    Day Shift          None   \n", "2                Manager  Facility Maintenance    Day Shift          None   \n", "3   Reliability Engineer  Facility Maintenance    Day Shift  ************   \n", "4    Electrical Engineer  Facility Maintenance    Day Shift          None   \n", "5        HVAC Specialist  Facility Maintenance    Day Shift          None   \n", "6           Sr. Engineer  Facility Maintenance    Day Shift          None   \n", "7             Engineer I  Facility Maintenance  Night Shift          None   \n", "8          Professional   Facility Maintenance    Day Shift          None   \n", "9             Engineer I  Facility Maintenance    Day Shift          None   \n", "10           Engineer II  Facility Maintenance    Day Shift          None   \n", "11         Specialist II  Facility Maintenance    Day Shift  ************   \n", "12         Specialist I   Facility Maintenance    Day Shift  330-980-8851   \n", "13            Supervisor  Facility Maintenance       D-Crew          None   \n", "14            Supervisor  Facility Maintenance       A-Crew          None   \n", "15            Supervisor  Facility Maintenance       B-Crew          None   \n", "16            Supervisor  Facility Maintenance       D-Crew          None   \n", "17            Supervisor  Facility Maintenance       C-Crew          None   \n", "18            Supervisor  Facility Maintenance       D-Crew          None   \n", "19            Supervisor  Facility Maintenance       A-Crew          None   \n", "\n", "                                                image        team  \n", "0   https://media.licdn.com/dms/image/v2/C560BAQEY...        None  \n", "1   https://media.licdn.com/dms/image/v2/C4E03AQHi...       Other  \n", "2   https://media.licdn.com/dms/image/v2/C4E03AQFo...       Other  \n", "3   https://media.licdn.com/dms/image/v2/D4E03AQE5...       Other  \n", "4                                                None       Other  \n", "5                                                None       Other  \n", "6   https://media.licdn.com/dms/image/v2/D4E03AQE_...       Other  \n", "7                                                None       Other  \n", "8                                                None       Other  \n", "9   https://media.licdn.com/dms/image/v2/D4D03AQHp...       Other  \n", "10                                               None       Other  \n", "11  https://media.licdn.com/dms/image/v2/C5603AQFK...       Other  \n", "12                                               None       Other  \n", "13                                               None   Operation  \n", "14                                               None   Operation  \n", "15                                               None  Technician  \n", "16  https://media.licdn.com/dms/image/v2/D5603AQFZ...        HVAC  \n", "17                                               None        HVAC  \n", "18                                               None  Technician  \n", "19                                               None  Technician  "]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["def display_table_head(table_name, n=5, export_excel=False):\n", "    # Connect to the SQLite database\n", "    conn = sqlite3.connect(DB_PATH)\n", "    \n", "    # Set the number of rows to display\n", "    n = 20\n", "    \n", "    try:\n", "        # Import pandas if not already imported\n", "        import pandas as pd\n", "        \n", "        # Read the first n rows from the table\n", "        query = f\"SELECT * FROM {table_name}\"\n", "        df = pd.read_sql_query(query, conn)\n", "        \n", "        # Export to Excel if requested\n", "        if export_excel:\n", "            export_path = f\"{table_name}_export.xlsx\"\n", "            df.to_excel(export_path, index=False)\n", "            print(f\"\\nExported data to {export_path}\")\n", "        \n", "        # Display the dataframe\n", "        print(f\"\\nFirst {n} rows of {table_name} table:\")\n", "        return df.head(n)\n", "        \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "    finally:\n", "        # Close the connection\n", "        conn.close()\n", "\n", "# Display the head of the selected table and export to Excel\n", "display_table_head(table_choice, export_excel=True)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🗑️ 5 rows deleted from 'OJT_WO_status' where description contains 'Semi-Annual Inspection'\n"]}], "source": ["import sqlite3\n", "\n", "DB_FILE = \"data/DataNew.db\"\n", "TABLE_NAME = \"OJT_WO_status\"\n", "\n", "# Connect to the database\n", "conn = sqlite3.connect(DB_FILE)\n", "cursor = conn.cursor()\n", "\n", "# # Delete rows where 'description' contains \"U1UFCO\"\n", "# delete_query = f\"\"\"\n", "#     DELETE FROM {TABLE_NAME}\n", "#     WHERE laborname LIKE '%<PERSON><PERSON>%' AND description LIKE '%<PERSON>%'\n", "# \"\"\"\n", "\n", "# Delete rows where 'description' contains \"U1UFCO\"\n", "delete_query = f\"\"\"\n", "    DELETE FROM {TABLE_NAME}\n", "    WHERE parent LIKE '%GM16041283%'\n", "\"\"\"\n", "\n", "cursor.execute(delete_query)\n", "conn.commit()\n", "\n", "# Print how many rows were deleted\n", "print(f\"🗑️ {cursor.rowcount} rows deleted from '{TABLE_NAME}' where description contains 'Semi-Annual Inspection'\")\n", "\n", "conn.close()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 30 rows updated from 'Operator' to 'Associate'.\n"]}], "source": ["import sqlite3\n", "\n", "# Define the database path\n", "db_path = \"data/DataNew.db\"\n", "\n", "# Connect to the SQLite database\n", "conn = sqlite3.connect(db_path)\n", "cursor = conn.cursor()\n", "\n", "# Define the old and new values\n", "old_value = \"Operator\"\n", "new_value = \"Associate\"\n", "\n", "# Execute the update statement\n", "cursor.execute(\"\"\"\n", "    UPDATE OrgChart\n", "    SET position = ?\n", "    WHERE position = ?\n", "\"\"\", (new_value, old_value))\n", "\n", "# Commit the changes and close the connection\n", "conn.commit()\n", "print(f\"✅ {cursor.rowcount} rows updated from '{old_value}' to '{new_value}'.\")\n", "\n", "conn.close()\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 5857 rows updated with Eastern Time values.\n"]}], "source": ["import sqlite3\n", "from datetime import datetime\n", "import pytz\n", "\n", "# Timezones\n", "utc = pytz.utc\n", "eastern = pytz.timezone(\"US/Eastern\")\n", "\n", "# Open the database\n", "db_path = \"data/DataNew.db\"\n", "conn = sqlite3.connect(db_path)\n", "cursor = conn.cursor()\n", "\n", "# Fetch all rows with changedate and statusdate\n", "cursor.execute(\"SELECT rowid, changedate, statusdate FROM OJT_WO_status\")\n", "rows = cursor.fetchall()\n", "\n", "updated = 0\n", "\n", "for rowid, changedate, statusdate in rows:\n", "    update_fields = {}\n", "    \n", "    # Convert changedate if it exists\n", "    if changedate:\n", "        try:\n", "            dt = datetime.fromisoformat(changedate)\n", "            dt_utc = dt.astimezone(utc)\n", "            dt_et = dt_utc.astimezone(eastern)\n", "            update_fields['changedate'] = dt_et.isoformat()\n", "        except Exception as e:\n", "            print(f\"⚠️ Skipped changedate for row {rowid}: {e}\")\n", "\n", "    # Convert statusdate if it exists\n", "    if statusdate:\n", "        try:\n", "            dt = datetime.fromisoformat(statusdate)\n", "            dt_utc = dt.astimezone(utc)\n", "            dt_et = dt_utc.astimezone(eastern)\n", "            update_fields['statusdate'] = dt_et.isoformat()\n", "        except Exception as e:\n", "            print(f\"⚠️ Skipped statusdate for row {rowid}: {e}\")\n", "\n", "    # Update row if we have converted values\n", "    if update_fields:\n", "        set_clause = \", \".join(f\"{col} = ?\" for col in update_fields)\n", "        values = list(update_fields.values())\n", "        values.append(rowid)\n", "        cursor.execute(f\"UPDATE OJT_WO_status SET {set_clause} WHERE rowid = ?\", values)\n", "        updated += 1\n", "\n", "conn.commit()\n", "conn.close()\n", "\n", "print(f\"✅ {updated} rows updated with Eastern Time values.\")\n"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Updated 5 row(s).\n"]}], "source": ["import sqlite3\n", "\n", "# Define the database path\n", "db_path = \"data/DataNew.db\"\n", "\n", "# Connect to the SQLite database\n", "conn = sqlite3.connect(db_path)\n", "cursor = conn.cursor()\n", "\n", "# Define the asset number to update\n", "new_assetnum = \"U1UFAC00501-001-028\"\n", "\n", "# Perform the update\n", "cursor.execute(\"\"\"\n", "    UPDATE OJT_WO_status\n", "    SET assetnum = ?\n", "    WHERE description LIKE '%Safety Stage 1: No BLIP'\n", "\"\"\", (new_assetnum,))\n", "\n", "# Commit the changes and close the connection\n", "conn.commit()\n", "print(f\"Updated {cursor.rowcount} row(s).\")\n", "\n", "conn.close()\n"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Updated 197 row(s) for: Safety Stage 1: Physical\n", "✅ Updated 196 row(s) for: Safety Stage 1: Blood Born Pathogens\n", "✅ Updated 192 row(s) for: Safety Stage 1: Lock Out Tag Out\n", "✅ Updated 194 row(s) for: Safety Stage 1: SDS Training\n", "✅ Updated 194 row(s) for: Safety Stage 1: PAPR Fit\n", "✅ Updated 194 row(s) for: Safety Stage 1: First Aid/CPR\n", "✅ Updated 194 row(s) for: Safety Stage 1: Fall Arrest / fall Hazard / Harness\n", "✅ Updated 179 row(s) for: Safety Stage 1: Filter Changes\n", "✅ Updated 182 row(s) for: Safety Stage 1: Confined Space Training\n", "✅ Updated 177 row(s) for: Safety Stage 1: Confined Space Rescue\n", "✅ Updated 196 row(s) for: Safety Stage 1: Roof Access  (Fall Arrest required)\n", "✅ Updated 127 row(s) for: Safety Stage 1: Ceiling Access  (Fall Arrest required)\n", "✅ Updated 50 row(s) for: Safety Stage 1: ESWP (LOTO)\n", "✅ Updated 50 row(s) for: Safety Stage 1: ESWP for at Risk Workers\n", "✅ Updated 85 row(s) for: Safety Stage 1: ESWP Substation Awareness\n", "✅ Updated 159 row(s) for: Safety Stage 1: Hot Work Training\n", "✅ Updated 172 row(s) for: Safety Stage 1: Spill Response\n", "✅ Updated 164 row(s) for: Safety Stage 1: Clean Room Training\n", "✅ Updated 179 row(s) for: Safety Stage 1: PSM Training and awareness\n", "✅ Updated 89 row(s) for: Safety Stage 1: DG training\n", "✅ Updated 192 row(s) for: Safety Stage 1: Mobile Cart    (PPE / Physical )\n", "✅ Updated 109 row(s) for: Safety Stage 1: Arial Lift  (Fall arrest required)\n", "✅ Updated 75 row(s) for: Safety Stage 1: Scissor lift   (Fall arrest requied)\n", "✅ Updated 150 row(s) for: Safety Stage 1: Fork Lift  (PPE / Physical )\n", "✅ Updated 40 row(s) for: Safety Stage 1: Crane    (PPE / Physical )\n", "✅ Updated 20 row(s) for: Safety Stage 1: Lull (PPE / Physical )\n", "✅ Updated 178 row(s) for: Safety Stage 1: No BLIP\n", "✅ Updated 5 row(s) for: <PERSON><PERSON> forklift training\n", "✅ Updated 5 row(s) for: Safety Stage 1: Confined Space Rescue - 6 M\n", "✅ Updated 70 row(s) for: Safety Stage 1: Confined Space Training - 6 M\n", "✅ Updated 67 row(s) for: Safety Stage 1: Confined Space Rescue - 1 Y\n", "✅ Updated 20 row(s) for: Safety Stage 1: Physical - 1 Y\n", "✅ Updated 115 row(s) for: Safety Stage 1: Lull    (PPE / Physical )\n", "✅ Updated 50 row(s) for: Safety Stage 1: <PERSON>l Lift  (Fall arrest requied)\n", "✅ Updated 103 row(s) for: Safety Stage 1: Scissor lift   (Fall arrest required)\n", "\n", "🎯 Total rows updated: 4369\n"]}], "source": ["import sqlite3\n", "\n", "# Define the database path\n", "db_path = \"data/DataNew.db\"\n", "\n", "# Connect to the SQLite database\n", "conn = sqlite3.connect(db_path)\n", "cursor = conn.cursor()\n", "\n", "# Mapping of description endings to asset numbers\n", "description_to_assetnum = {\n", "    \"Safety Stage 1: Physical\": \"U1UFAC00501-001-001\",\n", "    \"Safety Stage 1: Blood Born Pathogens\": \"U1UFAC00501-001-002\",\n", "    \"Safety Stage 1: Lock Out Tag Out\": \"U1UFAC00501-001-003\",\n", "    \"Safety Stage 1: SDS Training\": \"U1UFAC00501-001-004\",\n", "    \"Safety Stage 1: PAPR Fit\": \"U1UFAC00501-001-005\",\n", "    \"Safety Stage 1: First Aid/CPR\": \"U1UFAC00501-001-006\",\n", "    \"Safety Stage 1: Fall Arrest / fall Hazard / Harness\": \"U1UFAC00501-001-008\",\n", "    \"Safety Stage 1: Filter Changes\": \"U1UFAC00501-001-009\",\n", "    \"Safety Stage 1: Confined Space Training\": \"U1UFAC00501-001-010\",\n", "    \"Safety Stage 1: Confined Space Rescue\": \"U1UFAC00501-001-011\",\n", "    \"Safety Stage 1: Roof Access  (Fall Arrest required)\": \"U1UFAC00501-001-012\",\n", "    \"Safety Stage 1: Ceiling Access  (Fall Arrest required)\": \"U1UFAC00501-001-013\",\n", "    \"Safety Stage 1: ESWP (LOTO)\": \"U1UFAC00501-001-014\",\n", "    \"Safety Stage 1: ESWP for at Risk Workers\": \"U1UFAC00501-001-015\",\n", "    \"Safety Stage 1: ESWP Substation Awareness\": \"U1UFAC00501-001-016\",\n", "    \"Safety Stage 1: Hot Work Training\": \"U1UFAC00501-001-017\",\n", "    \"Safety Stage 1: Spill Response\": \"U1UFAC00501-001-018\",\n", "    \"Safety Stage 1: Clean Room Training\": \"U1UFAC00501-001-019\",\n", "    \"Safety Stage 1: PSM Training and awareness\": \"U1UFAC00501-001-020\",\n", "    \"Safety Stage 1: DG training\": \"U1UFAC00501-001-021\",\n", "    \"Safety Stage 1: Mobile Cart    (PPE / Physical )\": \"U1UFAC00501-001-022\",\n", "    \"Safety Stage 1: Arial Lift  (Fall arrest required)\": \"U1UFAC00501-001-023\",\n", "    \"Safety Stage 1: Scissor lift   (Fall arrest requied)\": \"U1UFAC00501-001-024\",\n", "    \"Safety Stage 1: Fork Lift  (PPE / Physical )\": \"U1UFAC00501-001-025\",\n", "    \"Safety Stage 1: Crane    (PPE / Physical )\": \"U1UFAC00501-001-026\",\n", "    \"Safety Stage 1: Lull (PPE / Physical )\": \"U1UFAC00501-001-027\",\n", "    \"Safety Stage 1: No BLIP\": \"U1UFAC00501-001-028\",\n", "    \"cameron <PERSON>off forklift training\": \"U1UFAC00501-001-025\", # Add more mappings\n", "    \"Safety Stage 1: Confined Space Rescue - 6 M\" : \"U1UFAC00501-001-011\",\n", "    \"Safety Stage 1: Confined Space Training - 6 M\": \"U1UFAC00501-001-010\",\n", "    \"Safety Stage 1: Confined Space Rescue - 1 Y\" : \"U1UFAC00501-001-011\",\n", "    \"Safety Stage 1: Physical - 1 Y\": \"U1UFAC00501-001-001\",\n", "    \"Safety Stage 1: Lull    (PPE / Physical )\": \"U1UFAC00501-001-027\",\n", "    \"Safety Stage 1: Arial Lift  (Fall arrest requied)\": \"U1UFAC00501-001-023\",\n", "    \"Safety Stage 1: Scissor lift   (Fall arrest required)\": \"U1UFAC00501-001-024\"\n", "\n", "}\n", "\n", "# Track how many rows were updated\n", "total_updated = 0\n", "\n", "# Perform the update for each mapping\n", "for desc_end, assetnum in description_to_assetnum.items():\n", "    cursor.execute(\"\"\"\n", "        UPDATE OJT_WO_status\n", "        SET assetnum = ?\n", "        WHERE TRIM(description) LIKE ?\n", "    \"\"\", (assetnum, f\"%{desc_end}\"))\n", "    total_updated += cursor.rowcount\n", "    print(f\"✅ Updated {cursor.rowcount} row(s) for: {desc_end}\")\n", "\n", "# Commit and close\n", "conn.commit()\n", "conn.close()\n", "\n", "print(f\"\\n🎯 Total rows updated: {total_updated}\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Table 'OrgChart' replaced from 'OrgChart_export.xlsx' with cleaned 'id' and 'parentId' as TEXT.\n"]}], "source": ["import sqlite3\n", "import pandas as pd\n", "\n", "def replace_table_with_excel(table_name, excel_path):\n", "    try:\n", "        # Load Excel into DataFrame\n", "        df = pd.read_excel(excel_path)\n", "\n", "        # Validate 'id' column\n", "        if 'id' not in df.columns:\n", "            print(\"❌ Excel file must contain an 'id' column.\")\n", "            return\n", "\n", "        # Convert parentId to integer if numeric, then to string\n", "        if 'parentId' in df.columns:\n", "            df['parentId'] = df['parentId'].apply(\n", "                lambda x: str(int(x)) if pd.notna(x) and str(x).replace('.', '', 1).isdigit() else (str(x) if pd.notna(x) else None)\n", "            )\n", "\n", "        # Convert id to string\n", "        df['id'] = df['id'].astype(str)\n", "\n", "        # Ensure 'id' is the first column\n", "        df = df[['id'] + [col for col in df.columns if col != 'id']]\n", "\n", "        # Connect to database\n", "        conn = sqlite3.connect(DB_PATH)\n", "        cursor = conn.cursor()\n", "\n", "        # Drop existing table\n", "        cursor.execute(f\"DROP TABLE IF EXISTS {table_name}\")\n", "\n", "        # Define column types (both TEXT)\n", "        col_defs = []\n", "        for col in df.columns:\n", "            if col == 'id':\n", "                col_defs.append(\"id TEXT PRIMARY KEY\")\n", "            elif col == 'parentId':\n", "                col_defs.append(\"parentId TEXT\")\n", "            else:\n", "                col_defs.append(f\"{col} TEXT\")  # Default type\n", "\n", "        # Create table\n", "        create_sql = f\"CREATE TABLE {table_name} ({', '.join(col_defs)})\"\n", "        cursor.execute(create_sql)\n", "\n", "        # Insert data\n", "        placeholders = \", \".join([\"?\"] * len(df.columns))\n", "        insert_sql = f\"INSERT INTO {table_name} VALUES ({placeholders})\"\n", "        cursor.executemany(insert_sql, df.values.tolist())\n", "\n", "        conn.commit()\n", "        print(f\"✅ Table '{table_name}' replaced from '{excel_path}' with cleaned 'id' and 'parentId' as TEXT.\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error: {e}\")\n", "    \n", "    finally:\n", "        conn.close()\n", "\n", "\n", "\n", "replace_table_with_excel(\"OrgChart\", \"OrgChart_export.xlsx\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Removed duplicates from 'OJT_WO_status'. Backup saved as 'backup_OJT_WO_status'.\n"]}], "source": ["import sqlite3\n", "\n", "def remove_exact_duplicates(db_path, table_name):\n", "    conn = sqlite3.connect(db_path)\n", "    cur  = conn.cursor()\n", "    try:\n", "        # 1) (Optional) back up the original table\n", "        cur.execute(f\"DROP TABLE IF EXISTS backup_{table_name}\")\n", "        cur.execute(f\"CREATE TABLE backup_{table_name} AS SELECT * FROM {table_name}\")\n", "\n", "        # 2) Create a temp deduped table\n", "        cur.execute(f\"CREATE TABLE temp_{table_name} AS SELECT DISTINCT * FROM {table_name}\")\n", "\n", "        # 3) Drop the original and rename the temp\n", "        cur.execute(f\"DROP TABLE {table_name}\")\n", "        cur.execute(f\"ALTER TABLE temp_{table_name} RENAME TO {table_name}\")\n", "\n", "        conn.commit()\n", "        print(f\"✅ Removed duplicates from '{table_name}'. Backup saved as 'backup_{table_name}'.\")\n", "    except Exception as e:\n", "        print(f\"❌ Error while deduping: {e}\")\n", "    finally:\n", "        conn.close()\n", "\n", "# Example usage\n", "remove_exact_duplicates(\"data/DataNew.db\", \"OJT_WO_status\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}
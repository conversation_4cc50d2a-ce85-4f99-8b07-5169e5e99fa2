import sqlite3
import os

# Database path - make sure this matches the path in project.py
DB_PATH = os.path.join('Data', 'DataNew.db')
print(f"Database path: {os.path.abspath(DB_PATH)}")

# Check if the database file exists
if os.path.exists(DB_PATH):
    print(f"\nDatabase file exists: {DB_PATH}")
else:
    print(f"Database file does not exist: {DB_PATH}")

def print_database_schema():
    # Connect to the SQLite database
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Get list of tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print("Tables in the database:")
    for table in tables:
        print(f"  {table[0]}")
    
    # Close the connection
    conn.close()

# Print the database schema
print_database_schema()
table_choice = 'OrgChart'

def print_table_schema():
    # Connect to the SQLite database
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        print(f"\n{table_choice} table columns:")
        cursor.execute(f"PRAGMA table_info('{table_choice}')")
        schema = cursor.fetchall()
        
        if not schema:
            print("No columns found or table does not exist.")
            return

        # Extract and print column names in comma-separated format
        column_names = [column[1] for column in schema]
        print(", ".join(column_names))
        
    except sqlite3.OperationalError as e:
        print(f"Error: {e}")
        
    finally:
        conn.close()

# Print the table columns
print_table_schema()

def display_table_head(table_name, n=5):
    # Connect to the SQLite database
    conn = sqlite3.connect(DB_PATH)
    
    # Set the number of rows to display
    n = 30
    
    try:
        # Import pandas if not already imported
        import pandas as pd
        
        # Read the first n rows from the table
        query = f"SELECT * FROM {table_name} LIMIT {n}"
        df = pd.read_sql_query(query, conn)
        
        # Display the dataframe
        print(f"\nFirst {n} rows of {table_name} table:")
        return df.head(n)
        
    except Exception as e:
        print(f"Error: {e}")
        return None
    finally:
        # Close the connection
        conn.close()

# Display the head of the selected table
display_table_head(table_choice)

def display_table_head(table_name, n=5, export_excel=False):
    # Connect to the SQLite database
    conn = sqlite3.connect(DB_PATH)
    
    # Set the number of rows to display
    n = 20
    
    try:
        # Import pandas if not already imported
        import pandas as pd
        
        # Read the first n rows from the table
        query = f"SELECT * FROM {table_name}"
        df = pd.read_sql_query(query, conn)
        
        # Export to Excel if requested
        if export_excel:
            export_path = f"{table_name}_export.xlsx"
            df.to_excel(export_path, index=False)
            print(f"\nExported data to {export_path}")
        
        # Display the dataframe
        print(f"\nFirst {n} rows of {table_name} table:")
        return df.head(n)
        
    except Exception as e:
        print(f"Error: {e}")
        return None
    finally:
        # Close the connection
        conn.close()

# Display the head of the selected table and export to Excel
display_table_head(table_choice, export_excel=True)

import sqlite3

DB_FILE = "data/DataNew.db"
TABLE_NAME = "OJT_WO_status"

# Connect to the database
conn = sqlite3.connect(DB_FILE)
cursor = conn.cursor()

# # Delete rows where 'description' contains "U1UFCO"
# delete_query = f"""
#     DELETE FROM {TABLE_NAME}
#     WHERE laborname LIKE '%Mircea Cristea%' AND description LIKE '%James Brenkert%'
# """

# Delete rows where 'description' contains "U1UFCO"
delete_query = f"""
    DELETE FROM {TABLE_NAME}
    WHERE parent LIKE '%GM16041283%'
"""

cursor.execute(delete_query)
conn.commit()

# Print how many rows were deleted
print(f"🗑️ {cursor.rowcount} rows deleted from '{TABLE_NAME}' where description contains 'Semi-Annual Inspection'")

conn.close()

import sqlite3

# Define the database path
db_path = "data/DataNew.db"

# Connect to the SQLite database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Define the old and new values
old_value = "Operator"
new_value = "Associate"

# Execute the update statement
cursor.execute("""
    UPDATE OrgChart
    SET position = ?
    WHERE position = ?
""", (new_value, old_value))

# Commit the changes and close the connection
conn.commit()
print(f"✅ {cursor.rowcount} rows updated from '{old_value}' to '{new_value}'.")

conn.close()


import sqlite3
from datetime import datetime
import pytz

# Timezones
utc = pytz.utc
eastern = pytz.timezone("US/Eastern")

# Open the database
db_path = "data/DataNew.db"
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Fetch all rows with changedate and statusdate
cursor.execute("SELECT rowid, changedate, statusdate FROM OJT_WO_status")
rows = cursor.fetchall()

updated = 0

for rowid, changedate, statusdate in rows:
    update_fields = {}
    
    # Convert changedate if it exists
    if changedate:
        try:
            dt = datetime.fromisoformat(changedate)
            dt_utc = dt.astimezone(utc)
            dt_et = dt_utc.astimezone(eastern)
            update_fields['changedate'] = dt_et.isoformat()
        except Exception as e:
            print(f"⚠️ Skipped changedate for row {rowid}: {e}")

    # Convert statusdate if it exists
    if statusdate:
        try:
            dt = datetime.fromisoformat(statusdate)
            dt_utc = dt.astimezone(utc)
            dt_et = dt_utc.astimezone(eastern)
            update_fields['statusdate'] = dt_et.isoformat()
        except Exception as e:
            print(f"⚠️ Skipped statusdate for row {rowid}: {e}")

    # Update row if we have converted values
    if update_fields:
        set_clause = ", ".join(f"{col} = ?" for col in update_fields)
        values = list(update_fields.values())
        values.append(rowid)
        cursor.execute(f"UPDATE OJT_WO_status SET {set_clause} WHERE rowid = ?", values)
        updated += 1

conn.commit()
conn.close()

print(f"✅ {updated} rows updated with Eastern Time values.")


import sqlite3

# Define the database path
db_path = "data/DataNew.db"

# Connect to the SQLite database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Define the asset number to update
new_assetnum = "U1UFAC00501-001-028"

# Perform the update
cursor.execute("""
    UPDATE OJT_WO_status
    SET assetnum = ?
    WHERE description LIKE '%Safety Stage 1: No BLIP'
""", (new_assetnum,))

# Commit the changes and close the connection
conn.commit()
print(f"Updated {cursor.rowcount} row(s).")

conn.close()


import sqlite3

# Define the database path
db_path = "data/DataNew.db"

# Connect to the SQLite database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Mapping of description endings to asset numbers
description_to_assetnum = {
    "Safety Stage 1: Physical": "U1UFAC00501-001-001",
    "Safety Stage 1: Blood Born Pathogens": "U1UFAC00501-001-002",
    "Safety Stage 1: Lock Out Tag Out": "U1UFAC00501-001-003",
    "Safety Stage 1: SDS Training": "U1UFAC00501-001-004",
    "Safety Stage 1: PAPR Fit": "U1UFAC00501-001-005",
    "Safety Stage 1: First Aid/CPR": "U1UFAC00501-001-006",
    "Safety Stage 1: Fall Arrest / fall Hazard / Harness": "U1UFAC00501-001-008",
    "Safety Stage 1: Filter Changes": "U1UFAC00501-001-009",
    "Safety Stage 1: Confined Space Training": "U1UFAC00501-001-010",
    "Safety Stage 1: Confined Space Rescue": "U1UFAC00501-001-011",
    "Safety Stage 1: Roof Access  (Fall Arrest required)": "U1UFAC00501-001-012",
    "Safety Stage 1: Ceiling Access  (Fall Arrest required)": "U1UFAC00501-001-013",
    "Safety Stage 1: ESWP (LOTO)": "U1UFAC00501-001-014",
    "Safety Stage 1: ESWP for at Risk Workers": "U1UFAC00501-001-015",
    "Safety Stage 1: ESWP Substation Awareness": "U1UFAC00501-001-016",
    "Safety Stage 1: Hot Work Training": "U1UFAC00501-001-017",
    "Safety Stage 1: Spill Response": "U1UFAC00501-001-018",
    "Safety Stage 1: Clean Room Training": "U1UFAC00501-001-019",
    "Safety Stage 1: PSM Training and awareness": "U1UFAC00501-001-020",
    "Safety Stage 1: DG training": "U1UFAC00501-001-021",
    "Safety Stage 1: Mobile Cart    (PPE / Physical )": "U1UFAC00501-001-022",
    "Safety Stage 1: Arial Lift  (Fall arrest required)": "U1UFAC00501-001-023",
    "Safety Stage 1: Scissor lift   (Fall arrest requied)": "U1UFAC00501-001-024",
    "Safety Stage 1: Fork Lift  (PPE / Physical )": "U1UFAC00501-001-025",
    "Safety Stage 1: Crane    (PPE / Physical )": "U1UFAC00501-001-026",
    "Safety Stage 1: Lull (PPE / Physical )": "U1UFAC00501-001-027",
    "Safety Stage 1: No BLIP": "U1UFAC00501-001-028",
    "cameron Fetteroff forklift training": "U1UFAC00501-001-025", # Add more mappings
    "Safety Stage 1: Confined Space Rescue - 6 M" : "U1UFAC00501-001-011",
    "Safety Stage 1: Confined Space Training - 6 M": "U1UFAC00501-001-010",
    "Safety Stage 1: Confined Space Rescue - 1 Y" : "U1UFAC00501-001-011",
    "Safety Stage 1: Physical - 1 Y": "U1UFAC00501-001-001",
    "Safety Stage 1: Lull    (PPE / Physical )": "U1UFAC00501-001-027",
    "Safety Stage 1: Arial Lift  (Fall arrest requied)": "U1UFAC00501-001-023",
    "Safety Stage 1: Scissor lift   (Fall arrest required)": "U1UFAC00501-001-024"

}

# Track how many rows were updated
total_updated = 0

# Perform the update for each mapping
for desc_end, assetnum in description_to_assetnum.items():
    cursor.execute("""
        UPDATE OJT_WO_status
        SET assetnum = ?
        WHERE TRIM(description) LIKE ?
    """, (assetnum, f"%{desc_end}"))
    total_updated += cursor.rowcount
    print(f"✅ Updated {cursor.rowcount} row(s) for: {desc_end}")

# Commit and close
conn.commit()
conn.close()

print(f"\n🎯 Total rows updated: {total_updated}")

import sqlite3
import pandas as pd

def replace_table_with_excel(table_name, excel_path):
    try:
        # Load Excel into DataFrame
        df = pd.read_excel(excel_path)

        # Validate 'id' column
        if 'id' not in df.columns:
            print("❌ Excel file must contain an 'id' column.")
            return

        # Convert parentId to integer if numeric, then to string
        if 'parentId' in df.columns:
            df['parentId'] = df['parentId'].apply(
                lambda x: str(int(x)) if pd.notna(x) and str(x).replace('.', '', 1).isdigit() else (str(x) if pd.notna(x) else None)
            )

        # Convert id to string
        df['id'] = df['id'].astype(str)

        # Ensure 'id' is the first column
        df = df[['id'] + [col for col in df.columns if col != 'id']]

        # Connect to database
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Drop existing table
        cursor.execute(f"DROP TABLE IF EXISTS {table_name}")

        # Define column types (both TEXT)
        col_defs = []
        for col in df.columns:
            if col == 'id':
                col_defs.append("id TEXT PRIMARY KEY")
            elif col == 'parentId':
                col_defs.append("parentId TEXT")
            else:
                col_defs.append(f"{col} TEXT")  # Default type

        # Create table
        create_sql = f"CREATE TABLE {table_name} ({', '.join(col_defs)})"
        cursor.execute(create_sql)

        # Insert data
        placeholders = ", ".join(["?"] * len(df.columns))
        insert_sql = f"INSERT INTO {table_name} VALUES ({placeholders})"
        cursor.executemany(insert_sql, df.values.tolist())

        conn.commit()
        print(f"✅ Table '{table_name}' replaced from '{excel_path}' with cleaned 'id' and 'parentId' as TEXT.")

    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        conn.close()



replace_table_with_excel("OrgChart", "OrgChart_export.xlsx")




import sqlite3

def remove_exact_duplicates(db_path, table_name):
    conn = sqlite3.connect(db_path)
    cur  = conn.cursor()
    try:
        # 1) (Optional) back up the original table
        cur.execute(f"DROP TABLE IF EXISTS backup_{table_name}")
        cur.execute(f"CREATE TABLE backup_{table_name} AS SELECT * FROM {table_name}")

        # 2) Create a temp deduped table
        cur.execute(f"CREATE TABLE temp_{table_name} AS SELECT DISTINCT * FROM {table_name}")

        # 3) Drop the original and rename the temp
        cur.execute(f"DROP TABLE {table_name}")
        cur.execute(f"ALTER TABLE temp_{table_name} RENAME TO {table_name}")

        conn.commit()
        print(f"✅ Removed duplicates from '{table_name}'. Backup saved as 'backup_{table_name}'.")
    except Exception as e:
        print(f"❌ Error while deduping: {e}")
    finally:
        conn.close()

# Example usage
remove_exact_duplicates("data/DataNew.db", "OJT_WO_status")



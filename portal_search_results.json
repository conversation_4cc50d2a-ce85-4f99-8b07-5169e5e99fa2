{"joshua jang": {"search_term": "joshua jang", "users": [], "raw_html": "\r\n\r\n\r\n\r\n\r\n\r\n\r\n \r\n\r\n\r\n\r\n\r\n\r\n\r\n<script type=\"text/javascript\">\r\nvar url = \"/ssoLoginFormMain.do\";\r\n\r\nif(opener) {\r\n\topener.location.href = url;\r\n\twindow.close();\r\n} else if(top != this) {\r\n\ttop.location.href = url;\r\n} else {\r\n\tlocation.href = url;\r\n}\r\n</script>\r\n "}, "Production Group": {"search_term": "Production Group", "users": [], "raw_html": "\r\n\r\n\r\n\r\n\r\n\r\n\r\n \r\n\r\n\r\n\r\n\r\n\r\n\r\n<script type=\"text/javascript\">\r\nvar url = \"/ssoLoginFormMain.do\";\r\n\r\nif(opener) {\r\n\topener.location.href = url;\r\n\twindow.close();\r\n} else if(top != this) {\r\n\ttop.location.href = url;\r\n} else {\r\n\tlocation.href = url;\r\n}\r\n</script>\r\n "}, "Utility/Facility Team": {"search_term": "Utility/Facility Team", "users": [], "raw_html": "\r\n\r\n\r\n\r\n\r\n\r\n\r\n \r\n\r\n\r\n\r\n\r\n\r\n\r\n<script type=\"text/javascript\">\r\nvar url = \"/ssoLoginFormMain.do\";\r\n\r\nif(opener) {\r\n\topener.location.href = url;\r\n\twindow.close();\r\n} else if(top != this) {\r\n\ttop.location.href = url;\r\n} else {\r\n\tlocation.href = url;\r\n}\r\n</script>\r\n "}, "ultiumcell.com": {"search_term": "ultiumcell.com", "users": [], "raw_html": "\r\n\r\n\r\n\r\n\r\n\r\n\r\n \r\n\r\n\r\n\r\n\r\n\r\n\r\n<script type=\"text/javascript\">\r\nvar url = \"/ssoLoginFormMain.do\";\r\n\r\nif(opener) {\r\n\topener.location.href = url;\r\n\twindow.close();\r\n} else if(top != this) {\r\n\ttop.location.href = url;\r\n} else {\r\n\tlocation.href = url;\r\n}\r\n</script>\r\n "}}